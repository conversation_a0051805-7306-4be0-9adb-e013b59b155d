const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class Database {
    constructor() {
        this.dbPath = process.env.DB_PATH || './database/infinite_loop.db';
        this.db = null;
        this.init();
    }

    init() {
        // Create database directory if it doesn't exist
        const dbDir = path.dirname(this.dbPath);
        if (!fs.existsSync(dbDir)) {
            fs.mkdirSync(dbDir, { recursive: true });
        }

        // Connect to database
        this.db = new sqlite3.Database(this.dbPath, (err) => {
            if (err) {
                console.error('Error opening database:', err.message);
            } else {
                console.log('Connected to SQLite database');
                this.createTables().catch(console.error);
            }
        });
    }

    async createTables() {
        const tables = [
            // Users table
            `CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                username VARCHAR(50) UNIQUE NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role VARCHAR(20) DEFAULT 'user',
                is_active BOOLEAN DEFAULT 1,
                last_login DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Testimonials table
            `CREATE TABLE IF NOT EXISTS testimonials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                position VARCHAR(100) NOT NULL,
                company VARCHAR(100),
                message TEXT NOT NULL,
                image_url VARCHAR(255),
                rating INTEGER DEFAULT 5,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Gallery table
            `CREATE TABLE IF NOT EXISTS gallery (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title VARCHAR(100) NOT NULL,
                description TEXT,
                image_url VARCHAR(255) NOT NULL,
                thumbnail_url VARCHAR(255),
                category VARCHAR(50) DEFAULT 'general',
                sort_order INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Contact messages table
            `CREATE TABLE IF NOT EXISTS contact_messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100) NOT NULL,
                subject VARCHAR(200),
                message TEXT NOT NULL,
                status VARCHAR(20) DEFAULT 'new',
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Services table
            `CREATE TABLE IF NOT EXISTS services (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title VARCHAR(100) NOT NULL,
                description TEXT NOT NULL,
                icon VARCHAR(50) NOT NULL,
                sort_order INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Website settings table
            `CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key VARCHAR(50) UNIQUE NOT NULL,
                setting_value TEXT,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Analytics table
            `CREATE TABLE IF NOT EXISTS analytics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                page_url VARCHAR(255) NOT NULL,
                visitor_ip VARCHAR(45),
                user_agent TEXT,
                referrer VARCHAR(255),
                visit_date DATE DEFAULT (date('now')),
                visit_time DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        // Create tables sequentially
        for (let i = 0; i < tables.length; i++) {
            try {
                await new Promise((resolve, reject) => {
                    this.db.run(tables[i], (err) => {
                        if (err) {
                            console.error(`Error creating table ${i + 1}:`, err.message);
                            reject(err);
                        } else {
                            console.log(`Table ${i + 1} created successfully`);
                            resolve();
                        }
                    });
                });
            } catch (error) {
                console.error(`Failed to create table ${i + 1}:`, error.message);
            }
        }

        // Insert default data after tables are created
        setTimeout(() => {
            this.insertDefaultData();
        }, 1000);
    }

    insertDefaultData() {
        // Default services
        const defaultServices = [
            {
                title: 'Market Analysis',
                description: 'Praesent sed pharetra lorem, blandit convallis mi. Aenean ornare elit ac metus lacinia, sed iaculis nibh semper.',
                icon: 'far fa-chart-bar',
                sort_order: 1
            },
            {
                title: 'Fast Support',
                description: 'Credit goes to Pexels website for all images used in this template. Cras condimentum mi et sapien dignissim luctus.',
                icon: 'far fa-comment-alt',
                sort_order: 2
            },
            {
                title: 'Top Security',
                description: 'You have no authority to post this template as a ZIP file on your template collection websites.',
                icon: 'fas fa-fingerprint',
                sort_order: 3
            },
            {
                title: 'Social Work',
                description: 'You can change Font Awesome icons by either fas or far in the HTML codes.',
                icon: 'fas fa-users',
                sort_order: 4
            }
        ];

        defaultServices.forEach(service => {
            this.db.run(
                `INSERT OR IGNORE INTO services (title, description, icon, sort_order) VALUES (?, ?, ?, ?)`,
                [service.title, service.description, service.icon, service.sort_order]
            );
        });

        // Default testimonials
        const defaultTestimonials = [
            {
                name: 'Catherine Win',
                position: 'Designer',
                company: 'Creative Studio',
                message: 'This background image includes a semi-transparent overlay layer. This section also has a parallax image effect.',
                image_url: 'img/testimonial-img-01.jpg',
                rating: 5
            },
            {
                name: 'Dual Rocker',
                position: 'CEO',
                company: 'Tech Corp',
                message: 'Testimonial section comes with carousel items. You can use Infinite Loop HTML CSS template for your websites.',
                image_url: 'img/testimonial-img-02.jpg',
                rating: 5
            }
        ];

        defaultTestimonials.forEach(testimonial => {
            this.db.run(
                `INSERT OR IGNORE INTO testimonials (name, position, company, message, image_url, rating) VALUES (?, ?, ?, ?, ?, ?)`,
                [testimonial.name, testimonial.position, testimonial.company, testimonial.message, testimonial.image_url, testimonial.rating]
            );
        });

        // Default settings
        const defaultSettings = [
            { key: 'site_title', value: 'Infinite Loop', description: 'Website title' },
            { key: 'site_description', value: 'Bootstrap 4.0 Parallax Theme', description: 'Website description' },
            { key: 'contact_email', value: '<EMAIL>', description: 'Contact email address' },
            { key: 'contact_phone', value: '************', description: 'Contact phone number' }
        ];

        defaultSettings.forEach(setting => {
            this.db.run(
                `INSERT OR IGNORE INTO settings (setting_key, setting_value, description) VALUES (?, ?, ?)`,
                [setting.key, setting.value, setting.description]
            );
        });
    }

    // Generic query method
    query(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // Generic run method for INSERT, UPDATE, DELETE
    run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    // Get single record
    get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    // Close database connection
    close() {
        return new Promise((resolve, reject) => {
            this.db.close((err) => {
                if (err) {
                    reject(err);
                } else {
                    console.log('Database connection closed');
                    resolve();
                }
            });
        });
    }
}

module.exports = new Database();
