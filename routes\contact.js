const express = require('express');
const { body, validationResult } = require('express-validator');
const Contact = require('../models/Contact');
const router = express.Router();

// Validation middleware
const validateContactMessage = [
    body('name').trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
    body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email address'),
    body('subject').optional().trim().isLength({ max: 200 }).withMessage('Subject must not exceed 200 characters'),
    body('message').trim().isLength({ min: 10, max: 2000 }).withMessage('Message must be between 10 and 2000 characters')
];

// GET /api/contact - Get all contact messages
router.get('/', async (req, res) => {
    try {
        const { page, limit, status, search, start_date, end_date } = req.query;
        
        if (search) {
            const messages = await Contact.search(search);
            res.json({
                success: true,
                data: messages
            });
        } else if (start_date && end_date) {
            const messages = await Contact.getByDateRange(start_date, end_date);
            res.json({
                success: true,
                data: messages
            });
        } else if (page && limit) {
            const result = await Contact.getPaginated(
                parseInt(page) || 1, 
                parseInt(limit) || 20,
                status
            );
            res.json({
                success: true,
                data: result
            });
        } else if (status) {
            const messages = await Contact.getByStatus(status);
            res.json({
                success: true,
                data: messages
            });
        } else {
            const messages = await Contact.getAll();
            res.json({
                success: true,
                data: messages
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// GET /api/contact/statistics - Get contact statistics
router.get('/statistics', async (req, res) => {
    try {
        const stats = await Contact.getStatistics();
        res.json({
            success: true,
            data: stats
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// GET /api/contact/unread-count - Get unread messages count
router.get('/unread-count', async (req, res) => {
    try {
        const count = await Contact.getUnreadCount();
        res.json({
            success: true,
            data: { count }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// GET /api/contact/:id - Get contact message by ID
router.get('/:id', async (req, res) => {
    try {
        const message = await Contact.getById(req.params.id);
        if (!message) {
            return res.status(404).json({
                success: false,
                message: 'Contact message not found'
            });
        }
        res.json({
            success: true,
            data: message
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// POST /api/contact - Create new contact message
router.post('/', validateContactMessage, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation errors',
                errors: errors.array()
            });
        }

        // Add IP address and user agent
        const messageData = {
            ...req.body,
            ip_address: req.ip || req.connection.remoteAddress,
            user_agent: req.get('User-Agent')
        };

        const messageId = await Contact.create(messageData);
        const message = await Contact.getById(messageId);
        
        res.status(201).json({
            success: true,
            message: 'Message sent successfully! We will get back to you soon.',
            data: message
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// PUT /api/contact/:id/status - Update message status
router.put('/:id/status', async (req, res) => {
    try {
        const { status } = req.body;
        const validStatuses = ['new', 'read', 'replied', 'archived'];
        
        if (!status || !validStatuses.includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid status. Valid statuses are: ' + validStatuses.join(', ')
            });
        }

        const updated = await Contact.updateStatus(req.params.id, status);
        if (!updated) {
            return res.status(404).json({
                success: false,
                message: 'Contact message not found'
            });
        }

        res.json({
            success: true,
            message: 'Message status updated successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// DELETE /api/contact/:id - Delete contact message
router.delete('/:id', async (req, res) => {
    try {
        const deleted = await Contact.delete(req.params.id);
        if (!deleted) {
            return res.status(404).json({
                success: false,
                message: 'Contact message not found'
            });
        }

        res.json({
            success: true,
            message: 'Contact message deleted successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

module.exports = router;
