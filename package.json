{"name": "elashrafy-website", "version": "1.0.0", "description": "ELAshrafy - Professional website with database integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-database.js"}, "keywords": ["website", "database", "express", "sqlite"], "author": "ELAshrafy Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "connect-sqlite3": "^0.9.13", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "form-data": "^4.0.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "node-fetch": "^2.7.0", "path": "^0.12.7", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.0.2"}}