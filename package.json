{"name": "infinite-loop-website", "version": "1.0.0", "description": "Professional website with database integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-database.js"}, "keywords": ["website", "database", "express", "sqlite"], "author": "Infinite Loop Team", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "multer": "^1.4.5-lts.1", "node-fetch": "^2.7.0", "path": "^0.12.7", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.0.2"}}