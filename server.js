require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');
const session = require('express-session');
const SQLiteStore = require('connect-sqlite3')(session);

// Import middleware
const {
    generalLimiter,
    contactLimiter,
    apiLimiter,
    helmetConfig,
    sanitizeInput,
    errorHandler,
    requestLogger,
    corsOptions
} = require('./middleware/security');

const { sessionConfig, isLoggedIn } = require('./middleware/auth');

// Import routes
const testimonialRoutes = require('./routes/testimonials');
const galleryRoutes = require('./routes/gallery');
const contactRoutes = require('./routes/contact');
const serviceRoutes = require('./routes/services');
const authRoutes = require('./routes/auth');
const uploadRoutes = require('./routes/upload');

// Import database
const db = require('./config/database');

const app = express();
const PORT = process.env.PORT || 3000;

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Security middleware
app.use(helmetConfig);
app.use(cors(corsOptions));
app.use(requestLogger);
app.use(generalLimiter);

// Session middleware
app.use(session({
    ...sessionConfig,
    store: new SQLiteStore({
        db: 'sessions.db',
        dir: './database'
    })
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Input sanitization
app.use(sanitizeInput);

// Authentication middleware for views
app.use(isLoggedIn);

// Serve static files from the 2117_infinite_loop directory
app.use(express.static(path.join(__dirname, '2117_infinite_loop')));

// Serve uploaded files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// API Routes with rate limiting
app.use('/api/auth', authRoutes);
app.use('/api/upload', apiLimiter, uploadRoutes);
app.use('/api/testimonials', apiLimiter, testimonialRoutes);
app.use('/api/gallery', apiLimiter, galleryRoutes);
app.use('/api/contact', contactLimiter, contactRoutes);
app.use('/api/services', apiLimiter, serviceRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: 'Server is running',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development'
    });
});

// API documentation endpoint
app.get('/api', (req, res) => {
    res.json({
        success: true,
        message: 'ELAshrafy API',
        version: '1.0.0',
        endpoints: {
            testimonials: {
                'GET /api/testimonials': 'Get all testimonials',
                'GET /api/testimonials/random': 'Get random testimonials',
                'GET /api/testimonials/:id': 'Get testimonial by ID',
                'POST /api/testimonials': 'Create new testimonial',
                'PUT /api/testimonials/:id': 'Update testimonial',
                'DELETE /api/testimonials/:id': 'Delete testimonial'
            },
            gallery: {
                'GET /api/gallery': 'Get all gallery items',
                'GET /api/gallery/categories': 'Get all categories',
                'GET /api/gallery/featured': 'Get featured items',
                'GET /api/gallery/:id': 'Get gallery item by ID',
                'POST /api/gallery': 'Create new gallery item',
                'PUT /api/gallery/:id': 'Update gallery item',
                'DELETE /api/gallery/:id': 'Delete gallery item'
            },
            contact: {
                'GET /api/contact': 'Get all contact messages',
                'GET /api/contact/statistics': 'Get contact statistics',
                'GET /api/contact/unread-count': 'Get unread messages count',
                'GET /api/contact/:id': 'Get contact message by ID',
                'POST /api/contact': 'Send new contact message',
                'PUT /api/contact/:id/status': 'Update message status',
                'DELETE /api/contact/:id': 'Delete contact message'
            },
            services: {
                'GET /api/services': 'Get all services',
                'GET /api/services/:id': 'Get service by ID',
                'POST /api/services': 'Create new service',
                'PUT /api/services/:id': 'Update service',
                'PUT /api/services/:id/toggle-status': 'Toggle service status',
                'PUT /api/services/reorder': 'Reorder services',
                'DELETE /api/services/:id': 'Delete service'
            }
        }
    });
});

// Protected routes that require authentication
app.get('/dashboard', (req, res) => {
    res.sendFile(path.join(__dirname, '2117_infinite_loop', 'dashboard.html'));
});

app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, '2117_infinite_loop', 'admin.html'));
});

// Serve the main HTML file for all non-API routes
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '2117_infinite_loop', 'index.html'));
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('\nReceived SIGINT. Graceful shutdown...');
    try {
        await db.close();
        console.log('Database connection closed.');
        process.exit(0);
    } catch (error) {
        console.error('Error during shutdown:', error);
        process.exit(1);
    }
});

process.on('SIGTERM', async () => {
    console.log('\nReceived SIGTERM. Graceful shutdown...');
    try {
        await db.close();
        console.log('Database connection closed.');
        process.exit(0);
    } catch (error) {
        console.error('Error during shutdown:', error);
        process.exit(1);
    }
});

// Start server
app.listen(PORT, () => {
    console.log(`
🚀 ELAshrafy Server Started Successfully!
📍 Server running on: http://localhost:${PORT}
🌍 Environment: ${process.env.NODE_ENV || 'development'}
📊 API Documentation: http://localhost:${PORT}/api
💾 Database: SQLite (${process.env.DB_PATH || './database/infinite_loop.db'})
🔒 Security: Enabled (Helmet, CORS, Rate Limiting)
📝 Logging: Enabled
    `);
});

module.exports = app;
