# 🚀 ELAshrafy - دليل التشغيل السريع

## التشغيل السريع

### Windows
```bash
# انقر نقرة مزدوجة على الملف أو شغل الأمر التالي في Command Prompt
start.bat
```

### Linux/Mac
```bash
# في Terminal
./start.sh
```

### التشغيل اليدوي
```bash
# 1. تثبيت المكتبات
npm install

# 2. تهيئة قاعدة البيانات
npm run init-db

# 3. تشغيل الخادم
npm start
```

## 🌐 الروابط المهمة

- **الموقع الرئيسي**: http://localhost:3000
- **صفحة تسجيل الدخول**: http://localhost:3000/login.html
- **لوحة التحكم الرئيسية**: http://localhost:3000/dashboard.html
- **لوحة الإدارة البسيطة**: http://localhost:3000/admin.html
- **توثيق API**: http://localhost:3000/api

## 🔐 بيانات تسجيل الدخول الافتراضية

- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: admin123456

⚠️ **مهم**: يرجى تغيير كلمة المرور الافتراضية بعد أول تسجيل دخول!

## 📱 الميزات المتاحة

### الموقع الرئيسي
- ✅ تصميم متجاوب مع جميع الأجهزة
- ✅ معرض الصور التفاعلي
- ✅ قسم الشهادات والتوصيات
- ✅ نموذج التواصل
- ✅ تحميل البيانات من قاعدة البيانات

### لوحة التحكم الرئيسية - نظام CRUD شامل
- ✅ نظام تسجيل دخول آمن ومتقدم
- ✅ لوحة تحكم شاملة ومتطورة
- ✅ **إدارة المستخدمين الكاملة** - إضافة، تعديل، حذف، تفعيل/إلغاء تفعيل
- ✅ **إدارة الشهادات مع الصور** - رفع صور العملاء، تعديل، حذف
- ✅ **إدارة معرض الصور المتقدم** - رفع متعدد، فئات، تعديل، حذف
- ✅ **إدارة الخدمات الاحترافية** - أيقونات، تفعيل/إلغاء، تعديل، حذف
- ✅ إدارة رسائل التواصل مع الردود
- ✅ **نظام رفع الملفات المتقدم** - صور متعددة، أمان، تنظيم
- ✅ إحصائيات مفصلة ومتقدمة
- ✅ معلومات النظام والأدوات
- ✅ تصميم متجاوب وحديث
- ✅ **وظائف CRUD كاملة** - إنشاء، قراءة، تحديث، حذف

### نظام المصادقة
- ✅ تسجيل الدخول والخروج
- ✅ إدارة المستخدمين
- ✅ صلاحيات متعددة المستويات
- ✅ حماية متقدمة

## 🛠️ إدارة المشروع

### إضافة مستخدم جديد
```javascript
// في لوحة الإدارة أو عبر API
POST /api/auth/register
{
    "email": "<EMAIL>",
    "password": "password123",
    "username": "username",
    "full_name": "Full Name"
}
```

### إضافة شهادة جديدة
```javascript
POST /api/testimonials
{
    "name": "اسم العميل",
    "position": "المنصب",
    "company": "الشركة",
    "message": "نص الشهادة",
    "rating": 5
}
```

### إضافة صورة للمعرض
```javascript
POST /api/gallery
{
    "title": "عنوان الصورة",
    "description": "وصف الصورة",
    "image_url": "رابط الصورة",
    "category": "الفئة"
}
```

## 🔧 استكشاف الأخطاء

### المشكلة: الخادم لا يعمل
```bash
# تحقق من تثبيت Node.js
node --version

# تحقق من المنفذ
netstat -an | findstr :3000
```

### المشكلة: قاعدة البيانات لا تعمل
```bash
# إعادة تهيئة قاعدة البيانات
npm run init-db
```

### المشكلة: لا يمكن تسجيل الدخول
- تأكد من استخدام البيانات الصحيحة
- تحقق من وجود ملف قاعدة البيانات
- تحقق من console في المتصفح للأخطاء

## 📞 الدعم الفني

إذا واجهت أي مشاكل:
1. تحقق من ملف `README.md` للتفاصيل الكاملة
2. راجع رسائل الخطأ في Terminal
3. تحقق من console المتصفح
4. تأكد من تشغيل جميع الخدمات المطلوبة

## 🎯 الخطوات التالية

1. **غير كلمة المرور الافتراضية**
2. **أضف المحتوى الخاص بك**
3. **خصص التصميم حسب احتياجاتك**
4. **اختبر جميع الوظائف**
5. **انشر الموقع على الخادم**

---

**تم تطوير المشروع بواسطة فريق ELAshrafy** 👑
