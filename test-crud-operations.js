// Test script for ELAshrafy CRUD Operations
const fetch = require('node-fetch');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const API_BASE = 'http://localhost:3000/api';

class CRUDTester {
    constructor() {
        this.token = null;
        this.results = {
            passed: 0,
            failed: 0,
            tests: []
        };
        this.createdItems = {
            users: [],
            testimonials: [],
            gallery: [],
            services: []
        };
    }

    async request(endpoint, options = {}) {
        const url = `${API_BASE}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...(this.token && { 'Authorization': `Bearer ${this.token}` }),
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();
            return { status: response.status, data };
        } catch (error) {
            return { error: error.message };
        }
    }

    logTest(name, success, message = '') {
        const status = success ? '✅' : '❌';
        console.log(`${status} ${name}${message ? ': ' + message : ''}`);
        
        this.results.tests.push({ name, success, message });
        if (success) {
            this.results.passed++;
        } else {
            this.results.failed++;
        }
    }

    async testLogin() {
        console.log('\n🔐 Testing Authentication...');
        
        const loginResult = await this.request('/auth/login', {
            method: 'POST',
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'admin123456'
            })
        });
        
        const success = loginResult.status === 200 && loginResult.data?.success;
        this.logTest('Admin Login', success);
        
        if (success) {
            this.token = loginResult.data.data.token;
        }
        
        return success;
    }

    async testUserCRUD() {
        console.log('\n👥 Testing User CRUD Operations...');
        
        // CREATE
        const newUser = {
            email: '<EMAIL>',
            password: 'testpass123',
            username: 'testuser',
            full_name: 'Test User CRUD',
            role: 'user'
        };
        
        const createResult = await this.request('/auth/register', {
            method: 'POST',
            body: JSON.stringify(newUser)
        });
        
        const createSuccess = createResult.status === 201 && createResult.data.success;
        this.logTest('Create User', createSuccess);
        
        if (createSuccess) {
            const userId = createResult.data.data.user.id;
            this.createdItems.users.push(userId);
            
            // READ
            const readResult = await this.request(`/auth/users/${userId}`);
            const readSuccess = readResult.status === 200 && readResult.data.success;
            this.logTest('Read User', readSuccess);
            
            // UPDATE
            const updateData = {
                ...newUser,
                full_name: 'Updated Test User',
                is_active: true
            };
            delete updateData.password;
            
            const updateResult = await this.request(`/auth/users/${userId}`, {
                method: 'PUT',
                body: JSON.stringify(updateData)
            });
            const updateSuccess = updateResult.status === 200 && updateResult.data.success;
            this.logTest('Update User', updateSuccess);
            
            // TOGGLE STATUS
            const toggleResult = await this.request(`/auth/users/${userId}/toggle-status`, {
                method: 'PUT',
                body: JSON.stringify({})
            });
            const toggleSuccess = toggleResult.status === 200 && toggleResult.data.success;
            this.logTest('Toggle User Status', toggleSuccess);
            
            // DELETE
            const deleteResult = await this.request(`/auth/users/${userId}`, {
                method: 'DELETE'
            });
            const deleteSuccess = deleteResult.status === 200 && deleteResult.data.success;
            this.logTest('Delete User', deleteSuccess);
            
            if (deleteSuccess) {
                this.createdItems.users = this.createdItems.users.filter(id => id !== userId);
            }
        }
    }

    async testTestimonialCRUD() {
        console.log('\n💬 Testing Testimonial CRUD Operations...');
        
        // CREATE
        const newTestimonial = {
            name: 'Test Client',
            position: 'Test Position',
            company: 'Test Company',
            message: 'This is a test testimonial for CRUD operations',
            rating: 5
        };
        
        const createResult = await this.request('/testimonials', {
            method: 'POST',
            body: JSON.stringify(newTestimonial)
        });
        
        const createSuccess = createResult.status === 201 && createResult.data.success;
        this.logTest('Create Testimonial', createSuccess);
        
        if (createSuccess) {
            const testimonialId = createResult.data.data.id;
            this.createdItems.testimonials.push(testimonialId);
            
            // READ
            const readResult = await this.request(`/testimonials/${testimonialId}`);
            const readSuccess = readResult.status === 200 && readResult.data.success;
            this.logTest('Read Testimonial', readSuccess);
            
            // UPDATE
            const updateData = {
                ...newTestimonial,
                message: 'Updated test testimonial message',
                rating: 4
            };
            
            const updateResult = await this.request(`/testimonials/${testimonialId}`, {
                method: 'PUT',
                body: JSON.stringify(updateData)
            });
            const updateSuccess = updateResult.status === 200 && updateResult.data.success;
            this.logTest('Update Testimonial', updateSuccess);
            
            // DELETE
            const deleteResult = await this.request(`/testimonials/${testimonialId}`, {
                method: 'DELETE'
            });
            const deleteSuccess = deleteResult.status === 200 && deleteResult.data.success;
            this.logTest('Delete Testimonial', deleteSuccess);
            
            if (deleteSuccess) {
                this.createdItems.testimonials = this.createdItems.testimonials.filter(id => id !== testimonialId);
            }
        }
    }

    async testGalleryCRUD() {
        console.log('\n🖼️ Testing Gallery CRUD Operations...');
        
        // CREATE
        const newGalleryItem = {
            title: 'Test Gallery Item',
            description: 'Test description for gallery item',
            category: 'test',
            image_url: 'https://via.placeholder.com/800x600',
            thumbnail_url: 'https://via.placeholder.com/300x200',
            sort_order: 0
        };
        
        const createResult = await this.request('/gallery', {
            method: 'POST',
            body: JSON.stringify(newGalleryItem)
        });
        
        const createSuccess = createResult.status === 201 && createResult.data.success;
        this.logTest('Create Gallery Item', createSuccess);
        
        if (createSuccess) {
            const galleryId = createResult.data.data.id;
            this.createdItems.gallery.push(galleryId);
            
            // READ
            const readResult = await this.request(`/gallery/${galleryId}`);
            const readSuccess = readResult.status === 200 && readResult.data.success;
            this.logTest('Read Gallery Item', readSuccess);
            
            // UPDATE
            const updateData = {
                ...newGalleryItem,
                title: 'Updated Gallery Item',
                description: 'Updated description',
                category: 'updated'
            };
            
            const updateResult = await this.request(`/gallery/${galleryId}`, {
                method: 'PUT',
                body: JSON.stringify(updateData)
            });
            const updateSuccess = updateResult.status === 200 && updateResult.data.success;
            this.logTest('Update Gallery Item', updateSuccess);
            
            // DELETE
            const deleteResult = await this.request(`/gallery/${galleryId}`, {
                method: 'DELETE'
            });
            const deleteSuccess = deleteResult.status === 200 && deleteResult.data.success;
            this.logTest('Delete Gallery Item', deleteSuccess);
            
            if (deleteSuccess) {
                this.createdItems.gallery = this.createdItems.gallery.filter(id => id !== galleryId);
            }
        }
    }

    async testServiceCRUD() {
        console.log('\n⚙️ Testing Service CRUD Operations...');
        
        // CREATE
        const newService = {
            title: 'Test Service',
            description: 'Test description for service',
            icon: 'fas fa-test',
            sort_order: 0,
            is_active: true
        };
        
        const createResult = await this.request('/services', {
            method: 'POST',
            body: JSON.stringify(newService)
        });
        
        const createSuccess = createResult.status === 201 && createResult.data.success;
        this.logTest('Create Service', createSuccess);
        
        if (createSuccess) {
            const serviceId = createResult.data.data.id;
            this.createdItems.services.push(serviceId);
            
            // READ
            const readResult = await this.request(`/services/${serviceId}`);
            const readSuccess = readResult.status === 200 && readResult.data.success;
            this.logTest('Read Service', readSuccess);
            
            // UPDATE
            const updateData = {
                ...newService,
                title: 'Updated Service',
                description: 'Updated description',
                icon: 'fas fa-updated'
            };
            
            const updateResult = await this.request(`/services/${serviceId}`, {
                method: 'PUT',
                body: JSON.stringify(updateData)
            });
            const updateSuccess = updateResult.status === 200 && updateResult.data.success;
            this.logTest('Update Service', updateSuccess);
            
            // TOGGLE STATUS
            const toggleResult = await this.request(`/services/${serviceId}/toggle-status`, {
                method: 'PUT',
                body: JSON.stringify({})
            });
            const toggleSuccess = toggleResult.status === 200 && toggleResult.data.success;
            this.logTest('Toggle Service Status', toggleSuccess);
            
            // DELETE
            const deleteResult = await this.request(`/services/${serviceId}`, {
                method: 'DELETE'
            });
            const deleteSuccess = deleteResult.status === 200 && deleteResult.data.success;
            this.logTest('Delete Service', deleteSuccess);
            
            if (deleteSuccess) {
                this.createdItems.services = this.createdItems.services.filter(id => id !== serviceId);
            }
        }
    }

    async cleanup() {
        console.log('\n🧹 Cleaning up remaining test data...');
        
        // Clean up any remaining items
        for (const userId of this.createdItems.users) {
            await this.request(`/auth/users/${userId}`, { method: 'DELETE' });
        }
        
        for (const testimonialId of this.createdItems.testimonials) {
            await this.request(`/testimonials/${testimonialId}`, { method: 'DELETE' });
        }
        
        for (const galleryId of this.createdItems.gallery) {
            await this.request(`/gallery/${galleryId}`, { method: 'DELETE' });
        }
        
        for (const serviceId of this.createdItems.services) {
            await this.request(`/services/${serviceId}`, { method: 'DELETE' });
        }
        
        this.logTest('Cleanup Test Data', true);
    }

    async runCompleteTest() {
        console.log('🚀 Starting Complete CRUD Operations Test for ELAshrafy\n');
        console.log('========================================');
        
        try {
            // Test authentication first
            const authSuccess = await this.testLogin();
            if (!authSuccess) {
                console.log('\n❌ Authentication failed - skipping CRUD tests');
                return;
            }
            
            // Test all CRUD operations
            await this.testUserCRUD();
            await this.testTestimonialCRUD();
            await this.testGalleryCRUD();
            await this.testServiceCRUD();
            
            // Cleanup
            await this.cleanup();
            
            // Print summary
            this.printSummary();
            
        } catch (error) {
            console.error('\n❌ Test suite failed:', error);
        }
    }

    printSummary() {
        console.log('\n========================================');
        console.log('📊 CRUD OPERATIONS TEST SUMMARY');
        console.log('========================================');
        console.log(`✅ Passed: ${this.results.passed}`);
        console.log(`❌ Failed: ${this.results.failed}`);
        console.log(`📈 Success Rate: ${((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)}%`);
        
        if (this.results.failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.results.tests
                .filter(test => !test.success)
                .forEach(test => console.log(`   - ${test.name}: ${test.message}`));
        }
        
        console.log('\n🎉 ELAshrafy CRUD Operations Test Complete!');
        
        if (this.results.failed === 0) {
            console.log('\n✅ All CRUD operations working perfectly!');
            console.log('\n🎛️ Your ELAshrafy Dashboard now supports:');
            console.log('✏️ Complete Create, Read, Update, Delete operations');
            console.log('👥 Full user management with edit/delete');
            console.log('💬 Testimonial management with edit/delete');
            console.log('🖼️ Gallery management with edit/delete');
            console.log('⚙️ Service management with edit/delete');
            console.log('🔒 Secure operations with proper authentication');
            console.log('🌐 Dashboard: http://localhost:3000/dashboard.html');
            console.log('\n🚀 Ready for professional use!');
        } else {
            console.log('\n⚠️  Some CRUD operations failed. Please check the errors above.');
        }
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    const tester = new CRUDTester();
    tester.runCompleteTest();
}

module.exports = CRUDTester;
