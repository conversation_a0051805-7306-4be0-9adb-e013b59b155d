const express = require('express');
const { body, validationResult } = require('express-validator');
const Testimonial = require('../models/Testimonial');
const router = express.Router();

// Validation middleware
const validateTestimonial = [
    body('name').trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
    body('position').trim().isLength({ min: 2, max: 100 }).withMessage('Position must be between 2 and 100 characters'),
    body('company').optional().trim().isLength({ max: 100 }).withMessage('Company name must not exceed 100 characters'),
    body('message').trim().isLength({ min: 10, max: 1000 }).withMessage('Message must be between 10 and 1000 characters'),
    body('rating').optional().isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
    body('image_url').optional().isURL().withMessage('Image URL must be valid')
];

// GET /api/testimonials - Get all testimonials
router.get('/', async (req, res) => {
    try {
        const { page, limit, rating } = req.query;
        
        if (page && limit) {
            const result = await Testimonial.getPaginated(
                parseInt(page) || 1, 
                parseInt(limit) || 10
            );
            res.json({
                success: true,
                data: result
            });
        } else if (rating) {
            const testimonials = await Testimonial.getByRating(parseInt(rating));
            res.json({
                success: true,
                data: testimonials
            });
        } else {
            const testimonials = await Testimonial.getAll();
            res.json({
                success: true,
                data: testimonials
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// GET /api/testimonials/random - Get random testimonials
router.get('/random', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 3;
        const testimonials = await Testimonial.getRandom(limit);
        res.json({
            success: true,
            data: testimonials
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// GET /api/testimonials/:id - Get testimonial by ID
router.get('/:id', async (req, res) => {
    try {
        const testimonial = await Testimonial.getById(req.params.id);
        if (!testimonial) {
            return res.status(404).json({
                success: false,
                message: 'Testimonial not found'
            });
        }
        res.json({
            success: true,
            data: testimonial
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// POST /api/testimonials - Create new testimonial
router.post('/', validateTestimonial, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation errors',
                errors: errors.array()
            });
        }

        const testimonialId = await Testimonial.create(req.body);
        const testimonial = await Testimonial.getById(testimonialId);
        
        res.status(201).json({
            success: true,
            message: 'Testimonial created successfully',
            data: testimonial
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// PUT /api/testimonials/:id - Update testimonial
router.put('/:id', validateTestimonial, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation errors',
                errors: errors.array()
            });
        }

        const updated = await Testimonial.update(req.params.id, req.body);
        if (!updated) {
            return res.status(404).json({
                success: false,
                message: 'Testimonial not found'
            });
        }

        const testimonial = await Testimonial.getById(req.params.id);
        res.json({
            success: true,
            message: 'Testimonial updated successfully',
            data: testimonial
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// DELETE /api/testimonials/:id - Delete testimonial
router.delete('/:id', async (req, res) => {
    try {
        const deleted = await Testimonial.delete(req.params.id);
        if (!deleted) {
            return res.status(404).json({
                success: false,
                message: 'Testimonial not found'
            });
        }

        res.json({
            success: true,
            message: 'Testimonial deleted successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

module.exports = router;
