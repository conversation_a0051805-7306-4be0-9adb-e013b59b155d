const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

// Rate limiting configuration
const createRateLimit = (windowMs, max, message) => {
    return rateLimit({
        windowMs,
        max,
        message: {
            success: false,
            message: message || 'Too many requests, please try again later.'
        },
        standardHeaders: true,
        legacyHeaders: false,
    });
};

// General rate limiting
const generalLimiter = createRateLimit(
    15 * 60 * 1000, // 15 minutes
    100, // limit each IP to 100 requests per windowMs
    'Too many requests from this IP, please try again after 15 minutes.'
);

// Strict rate limiting for contact form
const contactLimiter = createRateLimit(
    60 * 60 * 1000, // 1 hour
    5, // limit each IP to 5 contact form submissions per hour
    'Too many contact form submissions, please try again after 1 hour.'
);

// API rate limiting
const apiLimiter = createRateLimit(
    15 * 60 * 1000, // 15 minutes
    200, // limit each IP to 200 API requests per windowMs
    'Too many API requests, please try again after 15 minutes.'
);

// Helmet security configuration
const helmetConfig = helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdnjs.cloudflare.com"],
            scriptSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
            imgSrc: ["'self'", "data:", "https:", "http:"],
            connectSrc: ["'self'"],
            frameSrc: ["'none'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            manifestSrc: ["'self'"],
        },
    },
    crossOriginEmbedderPolicy: false,
});

// Input sanitization middleware
const sanitizeInput = (req, res, next) => {
    // Basic XSS protection - remove script tags and dangerous characters
    const sanitize = (obj) => {
        if (typeof obj === 'string') {
            return obj
                .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                .replace(/javascript:/gi, '')
                .replace(/on\w+\s*=/gi, '')
                .trim();
        }
        if (typeof obj === 'object' && obj !== null) {
            for (const key in obj) {
                obj[key] = sanitize(obj[key]);
            }
        }
        return obj;
    };

    if (req.body) {
        req.body = sanitize(req.body);
    }
    if (req.query) {
        req.query = sanitize(req.query);
    }
    if (req.params) {
        req.params = sanitize(req.params);
    }

    next();
};

// Error handling middleware
const errorHandler = (err, req, res, next) => {
    console.error('Error:', err);

    // Default error
    let error = {
        success: false,
        message: 'Internal server error'
    };

    // Validation errors
    if (err.name === 'ValidationError') {
        error.message = 'Validation error';
        error.details = err.details;
        return res.status(400).json(error);
    }

    // Database errors
    if (err.code === 'SQLITE_CONSTRAINT') {
        error.message = 'Database constraint violation';
        return res.status(400).json(error);
    }

    // File upload errors
    if (err.code === 'LIMIT_FILE_SIZE') {
        error.message = 'File too large';
        return res.status(413).json(error);
    }

    // Rate limit errors
    if (err.status === 429) {
        return res.status(429).json({
            success: false,
            message: 'Too many requests'
        });
    }

    // Development vs production error handling
    if (process.env.NODE_ENV === 'development') {
        error.stack = err.stack;
        error.details = err.message;
    }

    res.status(err.status || 500).json(error);
};

// Request logging middleware
const requestLogger = (req, res, next) => {
    const start = Date.now();
    
    res.on('finish', () => {
        const duration = Date.now() - start;
        const log = {
            method: req.method,
            url: req.url,
            status: res.statusCode,
            duration: `${duration}ms`,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            timestamp: new Date().toISOString()
        };
        
        console.log(JSON.stringify(log));
    });
    
    next();
};

// CORS configuration
const corsOptions = {
    origin: function (origin, callback) {
        const allowedOrigins = process.env.ALLOWED_ORIGINS 
            ? process.env.ALLOWED_ORIGINS.split(',')
            : ['http://localhost:3000'];
        
        // Allow requests with no origin (mobile apps, etc.)
        if (!origin) return callback(null, true);
        
        if (allowedOrigins.indexOf(origin) !== -1) {
            callback(null, true);
        } else {
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    optionsSuccessStatus: 200
};

module.exports = {
    generalLimiter,
    contactLimiter,
    apiLimiter,
    helmetConfig,
    sanitizeInput,
    errorHandler,
    requestLogger,
    corsOptions
};
