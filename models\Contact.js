const db = require('../config/database');

class Contact {
    // Get all contact messages
    static async getAll() {
        try {
            const messages = await db.query(
                'SELECT * FROM contact_messages ORDER BY created_at DESC'
            );
            return messages;
        } catch (error) {
            throw new Error('Error fetching contact messages: ' + error.message);
        }
    }

    // Get contact message by ID
    static async getById(id) {
        try {
            const message = await db.get(
                'SELECT * FROM contact_messages WHERE id = ?',
                [id]
            );
            return message;
        } catch (error) {
            throw new Error('Error fetching contact message: ' + error.message);
        }
    }

    // Create new contact message
    static async create(data) {
        try {
            const { name, email, subject, message, ip_address, user_agent } = data;
            const result = await db.run(
                `INSERT INTO contact_messages (name, email, subject, message, ip_address, user_agent) 
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [name, email, subject || null, message, ip_address || null, user_agent || null]
            );
            return result.id;
        } catch (error) {
            throw new Error('Error creating contact message: ' + error.message);
        }
    }

    // Update message status
    static async updateStatus(id, status) {
        try {
            const validStatuses = ['new', 'read', 'replied', 'archived'];
            if (!validStatuses.includes(status)) {
                throw new Error('Invalid status');
            }

            const result = await db.run(
                'UPDATE contact_messages SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [status, id]
            );
            return result.changes > 0;
        } catch (error) {
            throw new Error('Error updating message status: ' + error.message);
        }
    }

    // Delete contact message
    static async delete(id) {
        try {
            const result = await db.run(
                'DELETE FROM contact_messages WHERE id = ?',
                [id]
            );
            return result.changes > 0;
        } catch (error) {
            throw new Error('Error deleting contact message: ' + error.message);
        }
    }

    // Get messages by status
    static async getByStatus(status) {
        try {
            const messages = await db.query(
                'SELECT * FROM contact_messages WHERE status = ? ORDER BY created_at DESC',
                [status]
            );
            return messages;
        } catch (error) {
            throw new Error('Error fetching messages by status: ' + error.message);
        }
    }

    // Get messages with pagination
    static async getPaginated(page = 1, limit = 20, status = null) {
        try {
            const offset = (page - 1) * limit;
            let query = 'SELECT * FROM contact_messages';
            let countQuery = 'SELECT COUNT(*) as total FROM contact_messages';
            let params = [];
            let countParams = [];

            if (status) {
                query += ' WHERE status = ?';
                countQuery += ' WHERE status = ?';
                params.push(status);
                countParams.push(status);
            }

            query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
            params.push(limit, offset);

            const messages = await db.query(query, params);
            const totalResult = await db.get(countQuery, countParams);

            return {
                messages,
                total: totalResult.total,
                page,
                limit,
                totalPages: Math.ceil(totalResult.total / limit),
                status
            };
        } catch (error) {
            throw new Error('Error fetching paginated messages: ' + error.message);
        }
    }

    // Get unread messages count
    static async getUnreadCount() {
        try {
            const result = await db.get(
                'SELECT COUNT(*) as count FROM contact_messages WHERE status = ?',
                ['new']
            );
            return result.count;
        } catch (error) {
            throw new Error('Error fetching unread count: ' + error.message);
        }
    }

    // Search messages
    static async search(searchTerm) {
        try {
            const messages = await db.query(
                `SELECT * FROM contact_messages 
                 WHERE name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?
                 ORDER BY created_at DESC`,
                [`%${searchTerm}%`, `%${searchTerm}%`, `%${searchTerm}%`, `%${searchTerm}%`]
            );
            return messages;
        } catch (error) {
            throw new Error('Error searching messages: ' + error.message);
        }
    }

    // Get messages by date range
    static async getByDateRange(startDate, endDate) {
        try {
            const messages = await db.query(
                'SELECT * FROM contact_messages WHERE DATE(created_at) BETWEEN ? AND ? ORDER BY created_at DESC',
                [startDate, endDate]
            );
            return messages;
        } catch (error) {
            throw new Error('Error fetching messages by date range: ' + error.message);
        }
    }

    // Get message statistics
    static async getStatistics() {
        try {
            const totalMessages = await db.get(
                'SELECT COUNT(*) as total FROM contact_messages'
            );

            const newMessages = await db.get(
                'SELECT COUNT(*) as count FROM contact_messages WHERE status = ?',
                ['new']
            );

            const todayMessages = await db.get(
                'SELECT COUNT(*) as count FROM contact_messages WHERE DATE(created_at) = DATE("now")'
            );

            const thisWeekMessages = await db.get(
                'SELECT COUNT(*) as count FROM contact_messages WHERE DATE(created_at) >= DATE("now", "-7 days")'
            );

            const statusCounts = await db.query(
                'SELECT status, COUNT(*) as count FROM contact_messages GROUP BY status'
            );

            return {
                total: totalMessages.total,
                new: newMessages.count,
                today: todayMessages.count,
                thisWeek: thisWeekMessages.count,
                byStatus: statusCounts.reduce((acc, row) => {
                    acc[row.status] = row.count;
                    return acc;
                }, {})
            };
        } catch (error) {
            throw new Error('Error fetching message statistics: ' + error.message);
        }
    }
}

module.exports = Contact;
