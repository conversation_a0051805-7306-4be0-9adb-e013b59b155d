// API Configuration
const API_BASE_URL = window.location.origin + '/api';

// API Helper Class
class InfiniteLoopAPI {
    constructor() {
        this.baseURL = API_BASE_URL;
    }

    // Generic API request method
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    }

    // GET request
    async get(endpoint) {
        return this.request(endpoint, { method: 'GET' });
    }

    // POST request
    async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT request
    async put(endpoint, data) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE request
    async delete(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    }

    // Testimonials API
    async getTestimonials() {
        return this.get('/testimonials');
    }

    async getRandomTestimonials(limit = 3) {
        return this.get(`/testimonials/random?limit=${limit}`);
    }

    async createTestimonial(testimonialData) {
        return this.post('/testimonials', testimonialData);
    }

    // Gallery API
    async getGallery() {
        return this.get('/gallery');
    }

    async getFeaturedGallery(limit = 6) {
        return this.get(`/gallery/featured?limit=${limit}`);
    }

    async getGalleryCategories() {
        return this.get('/gallery/categories');
    }

    async createGalleryItem(galleryData) {
        return this.post('/gallery', galleryData);
    }

    // Services API
    async getServices() {
        return this.get('/services');
    }

    async createService(serviceData) {
        return this.post('/services', serviceData);
    }

    // Contact API
    async sendContactMessage(contactData) {
        return this.post('/contact', contactData);
    }

    async getContactStatistics() {
        return this.get('/contact/statistics');
    }

    // Health check
    async healthCheck() {
        return this.get('/health');
    }
}

// Create global API instance
const api = new InfiniteLoopAPI();

// Utility Functions
class UIHelpers {
    // Show loading spinner
    static showLoading(element) {
        if (element) {
            element.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i></div>';
        }
    }

    // Show error message
    static showError(message, container = null) {
        const errorHTML = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle"></i> ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;

        if (container) {
            container.innerHTML = errorHTML;
        } else {
            console.error(message);
        }
    }

    // Show success message
    static showSuccess(message, container = null) {
        const successHTML = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i> ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;

        if (container) {
            container.innerHTML = successHTML;
        }
    }

    // Format date
    static formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    // Truncate text
    static truncateText(text, maxLength = 100) {
        if (text.length <= maxLength) return text;
        return text.substr(0, maxLength) + '...';
    }

    // Generate star rating HTML
    static generateStarRating(rating) {
        let stars = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                stars += '<i class="fas fa-star text-warning"></i>';
            } else {
                stars += '<i class="far fa-star text-muted"></i>';
            }
        }
        return stars;
    }
}

// Data Loading Functions
class DataLoader {
    // Load testimonials into carousel
    static async loadTestimonials() {
        try {
            const response = await api.getRandomTestimonials(5);
            const testimonials = response.data;
            
            const carousel = document.querySelector('.tm-testimonials-carousel');
            if (!carousel) return;

            carousel.innerHTML = '';

            testimonials.forEach(testimonial => {
                const testimonialHTML = `
                    <figure class="tm-testimonial-item">
                        <img src="${testimonial.image_url || 'img/testimonial-img-01.jpg'}" 
                             alt="${testimonial.name}" class="img-fluid mx-auto">
                        <blockquote>${testimonial.message}</blockquote>
                        <figcaption>
                            ${testimonial.name} (${testimonial.position}${testimonial.company ? ', ' + testimonial.company : ''})
                            <div class="mt-2">${UIHelpers.generateStarRating(testimonial.rating)}</div>
                        </figcaption>
                    </figure>
                `;
                carousel.insertAdjacentHTML('beforeend', testimonialHTML);
            });

            // Reinitialize Slick carousel
            if (typeof $ !== 'undefined' && $('.tm-testimonials-carousel').slick) {
                $('.tm-testimonials-carousel').slick('unslick').slick({
                    dots: true,
                    prevArrow: false,
                    nextArrow: false,
                    infinite: false,
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    responsive: [
                        {
                            breakpoint: 992,
                            settings: { slidesToShow: 2 }
                        },
                        {
                            breakpoint: 768,
                            settings: { slidesToShow: 2 }
                        },
                        {
                            breakpoint: 480,
                            settings: { slidesToShow: 1 }
                        }
                    ]
                });
            }
        } catch (error) {
            console.error('Error loading testimonials:', error);
        }
    }

    // Load gallery items
    static async loadGallery() {
        try {
            const response = await api.getFeaturedGallery(8);
            const galleryItems = response.data;
            
            const gallery = document.querySelector('.tm-gallery');
            if (!gallery) return;

            // Clear existing items except the first few (keep original items as fallback)
            const existingItems = gallery.querySelectorAll('a');
            if (galleryItems.length > 0) {
                gallery.innerHTML = '';

                galleryItems.forEach(item => {
                    const galleryHTML = `
                        <a href="${item.image_url}">
                            <figure class="effect-honey tm-gallery-item">
                                <img src="${item.thumbnail_url || item.image_url}" 
                                     alt="${item.title}" class="img-fluid">
                                <figcaption>
                                    <h2><i>${item.title} <span>${item.description || ''}</span></i></h2>
                                </figcaption>
                            </figure>
                        </a>
                    `;
                    gallery.insertAdjacentHTML('beforeend', galleryHTML);
                });

                // Reinitialize Magnific Popup
                if (typeof $ !== 'undefined' && $('.tm-gallery').magnificPopup) {
                    $('.tm-gallery').magnificPopup({
                        delegate: 'a',
                        type: 'image',
                        gallery: { enabled: true }
                    });
                }
            }
        } catch (error) {
            console.error('Error loading gallery:', error);
        }
    }

    // Load services
    static async loadServices() {
        try {
            const response = await api.getServices();
            const services = response.data;
            
            // Update services section if needed
            console.log('Services loaded:', services);
        } catch (error) {
            console.error('Error loading services:', error);
        }
    }
}

// Contact Form Handler
class ContactForm {
    static init() {
        const contactForm = document.querySelector('#contact form');
        if (!contactForm) return;

        contactForm.addEventListener('submit', this.handleSubmit.bind(this));
    }

    static async handleSubmit(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        const submitButton = form.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        
        try {
            // Show loading state
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            
            // Send message
            const response = await api.sendContactMessage(data);
            
            // Show success message
            const messageContainer = document.createElement('div');
            messageContainer.className = 'mt-3';
            form.appendChild(messageContainer);
            
            UIHelpers.showSuccess(response.message, messageContainer);
            
            // Reset form
            form.reset();
            
            // Remove success message after 5 seconds
            setTimeout(() => {
                if (messageContainer.parentNode) {
                    messageContainer.remove();
                }
            }, 5000);
            
        } catch (error) {
            // Show error message
            const messageContainer = document.createElement('div');
            messageContainer.className = 'mt-3';
            form.appendChild(messageContainer);
            
            UIHelpers.showError(error.message || 'Failed to send message. Please try again.', messageContainer);
            
            // Remove error message after 5 seconds
            setTimeout(() => {
                if (messageContainer.parentNode) {
                    messageContainer.remove();
                }
            }, 5000);
        } finally {
            // Restore button state
            submitButton.disabled = false;
            submitButton.textContent = originalText;
        }
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize contact form
    ContactForm.init();
    
    // Load dynamic content
    DataLoader.loadTestimonials();
    DataLoader.loadGallery();
    DataLoader.loadServices();
    
    // Health check
    api.healthCheck().then(response => {
        console.log('API Health Check:', response);
    }).catch(error => {
        console.error('API Health Check Failed:', error);
    });
});

// Export for global use
window.InfiniteLoopAPI = InfiniteLoopAPI;
window.api = api;
window.UIHelpers = UIHelpers;
window.DataLoader = DataLoader;
