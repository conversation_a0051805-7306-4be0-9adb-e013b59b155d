// Test script for ELAshrafy Upload System
const fetch = require('node-fetch');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const API_BASE = 'http://localhost:3000/api';

class UploadSystemTester {
    constructor() {
        this.token = null;
        this.results = {
            passed: 0,
            failed: 0,
            tests: []
        };
    }

    async request(endpoint, options = {}) {
        const url = `${API_BASE}${endpoint}`;
        const config = {
            headers: {
                ...(this.token && { 'Authorization': `Bearer ${this.token}` }),
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();
            return { status: response.status, data };
        } catch (error) {
            return { error: error.message };
        }
    }

    logTest(name, success, message = '') {
        const status = success ? '✅' : '❌';
        console.log(`${status} ${name}${message ? ': ' + message : ''}`);
        
        this.results.tests.push({ name, success, message });
        if (success) {
            this.results.passed++;
        } else {
            this.results.failed++;
        }
    }

    async testLogin() {
        console.log('\n🔐 Testing Authentication...');
        
        const loginResult = await this.request('/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'admin123456'
            })
        });
        
        const success = loginResult.status === 200 && loginResult.data?.success;
        this.logTest('Admin Login', success);
        
        if (success) {
            this.token = loginResult.data.data.token;
            this.logTest('Token Generation', !!this.token);
        }
        
        return success;
    }

    createTestImage() {
        // Create a simple test image (1x1 pixel PNG)
        const pngData = Buffer.from([
            0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
            0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
            0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
            0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53,
            0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41,
            0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
            0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42,
            0x60, 0x82
        ]);
        
        const testImagePath = path.join(__dirname, 'test-image.png');
        fs.writeFileSync(testImagePath, pngData);
        return testImagePath;
    }

    async testImageUpload() {
        console.log('\n📤 Testing Image Upload System...');
        
        const testImagePath = this.createTestImage();
        
        try {
            // Test single image upload
            const formData = new FormData();
            formData.append('image', fs.createReadStream(testImagePath));
            formData.append('type', 'gallery');

            const uploadResult = await fetch(`${API_BASE}/upload/image`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`
                },
                body: formData
            });

            const uploadData = await uploadResult.json();
            const uploadSuccess = uploadResult.status === 200 && uploadData.success;
            this.logTest('Single Image Upload', uploadSuccess, uploadData.message);

            if (uploadSuccess) {
                this.logTest('Upload Response Data', !!uploadData.data.url);
                this.logTest('File URL Generation', uploadData.data.url.includes('/uploads/'));
                
                // Test file listing
                const listResult = await this.request('/upload/list/gallery');
                const listSuccess = listResult.status === 200 && listResult.data.success;
                this.logTest('List Uploaded Files', listSuccess);
                
                if (listSuccess && listResult.data.data.length > 0) {
                    const uploadedFile = listResult.data.data.find(f => f.filename === uploadData.data.filename);
                    this.logTest('File Found in List', !!uploadedFile);
                    
                    // Test file deletion
                    const deleteResult = await this.request(`/upload/gallery/${uploadData.data.filename}`, {
                        method: 'DELETE'
                    });
                    const deleteSuccess = deleteResult.status === 200 && deleteResult.data.success;
                    this.logTest('File Deletion', deleteSuccess);
                }
            }

        } catch (error) {
            this.logTest('Image Upload System', false, error.message);
        } finally {
            // Clean up test file
            if (fs.existsSync(testImagePath)) {
                fs.unlinkSync(testImagePath);
            }
        }
    }

    async testBase64Upload() {
        console.log('\n🖼️ Testing Base64 Upload...');
        
        // Simple 1x1 red pixel PNG in base64
        const base64Image = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
        
        try {
            const base64Result = await this.request('/upload/base64', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    imageData: base64Image,
                    filename: 'test-base64.png',
                    type: 'testimonials'
                })
            });

            const success = base64Result.status === 200 && base64Result.data.success;
            this.logTest('Base64 Image Upload', success, base64Result.data?.message);

            if (success) {
                this.logTest('Base64 File URL', base64Result.data.data.url.includes('/uploads/testimonials/'));
                
                // Clean up
                await this.request(`/upload/testimonials/${base64Result.data.data.filename}`, {
                    method: 'DELETE'
                });
            }

        } catch (error) {
            this.logTest('Base64 Upload', false, error.message);
        }
    }

    async testContentManagementWithUpload() {
        console.log('\n📝 Testing Content Management with Upload...');
        
        const testImagePath = this.createTestImage();
        
        try {
            // Test testimonial creation with image
            const formData = new FormData();
            formData.append('image', fs.createReadStream(testImagePath));
            formData.append('type', 'testimonials');

            const uploadResult = await fetch(`${API_BASE}/upload/image`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`
                },
                body: formData
            });

            const uploadData = await uploadResult.json();
            
            if (uploadResult.status === 200 && uploadData.success) {
                // Create testimonial with uploaded image
                const testimonialData = {
                    name: 'Test Client',
                    position: 'Test Position',
                    company: 'Test Company',
                    message: 'This is a test testimonial with uploaded image',
                    rating: 5,
                    image_url: uploadData.data.fullUrl
                };

                const testimonialResult = await this.request('/testimonials', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testimonialData)
                });

                const testimonialSuccess = testimonialResult.status === 201 && testimonialResult.data.success;
                this.logTest('Testimonial with Image', testimonialSuccess);

                if (testimonialSuccess) {
                    // Clean up testimonial
                    await this.request(`/testimonials/${testimonialResult.data.data.id}`, {
                        method: 'DELETE'
                    });
                }

                // Clean up uploaded file
                await this.request(`/upload/testimonials/${uploadData.data.filename}`, {
                    method: 'DELETE'
                });
            }

        } catch (error) {
            this.logTest('Content Management with Upload', false, error.message);
        } finally {
            if (fs.existsSync(testImagePath)) {
                fs.unlinkSync(testImagePath);
            }
        }
    }

    async testDashboardIntegration() {
        console.log('\n🎛️ Testing Dashboard Integration...');
        
        try {
            // Test dashboard page access
            const dashboardResponse = await fetch('http://localhost:3000/dashboard.html');
            this.logTest('Dashboard Page Access', dashboardResponse.status === 200);

            // Test all required API endpoints for dashboard
            const endpoints = [
                '/auth/users',
                '/auth/statistics',
                '/testimonials',
                '/gallery',
                '/services',
                '/contact',
                '/contact/statistics'
            ];

            for (const endpoint of endpoints) {
                const result = await this.request(endpoint);
                const success = result.status === 200;
                this.logTest(`Dashboard API: ${endpoint}`, success);
            }

        } catch (error) {
            this.logTest('Dashboard Integration', false, error.message);
        }
    }

    async testSecurityFeatures() {
        console.log('\n🔒 Testing Security Features...');
        
        try {
            // Test unauthorized upload
            const formData = new FormData();
            formData.append('image', Buffer.from('fake image'), 'test.png');

            const unauthorizedResult = await fetch(`${API_BASE}/upload/image`, {
                method: 'POST',
                body: formData
            });

            this.logTest('Unauthorized Upload Blocked', unauthorizedResult.status === 401);

            // Test invalid file type
            const invalidFormData = new FormData();
            invalidFormData.append('image', Buffer.from('not an image'), 'test.txt');
            invalidFormData.append('type', 'gallery');

            const invalidFileResult = await fetch(`${API_BASE}/upload/image`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`
                },
                body: invalidFormData
            });

            this.logTest('Invalid File Type Rejected', invalidFileResult.status === 400);

        } catch (error) {
            this.logTest('Security Features', false, error.message);
        }
    }

    async runCompleteTest() {
        console.log('🚀 Starting Complete Upload System Test for ELAshrafy\n');
        console.log('========================================');
        
        try {
            // Test authentication first
            const authSuccess = await this.testLogin();
            if (!authSuccess) {
                console.log('\n❌ Authentication failed - skipping authenticated tests');
                return;
            }
            
            // Test all upload features
            await this.testImageUpload();
            await this.testBase64Upload();
            await this.testContentManagementWithUpload();
            await this.testDashboardIntegration();
            await this.testSecurityFeatures();
            
            // Print summary
            this.printSummary();
            
        } catch (error) {
            console.error('\n❌ Test suite failed:', error);
        }
    }

    printSummary() {
        console.log('\n========================================');
        console.log('📊 UPLOAD SYSTEM TEST SUMMARY');
        console.log('========================================');
        console.log(`✅ Passed: ${this.results.passed}`);
        console.log(`❌ Failed: ${this.results.failed}`);
        console.log(`📈 Success Rate: ${((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)}%`);
        
        if (this.results.failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.results.tests
                .filter(test => !test.success)
                .forEach(test => console.log(`   - ${test.name}: ${test.message}`));
        }
        
        console.log('\n🎉 ELAshrafy Upload System Test Complete!');
        
        if (this.results.failed === 0) {
            console.log('\n✅ All upload features working perfectly!');
            console.log('\n🎛️ Your ELAshrafy Dashboard now supports:');
            console.log('📤 Complete file upload system');
            console.log('🖼️ Image management for gallery and testimonials');
            console.log('📝 Full content management with images');
            console.log('🔒 Secure file handling and validation');
            console.log('🌐 Dashboard: http://localhost:3000/dashboard.html');
            console.log('📧 Login: <EMAIL> / admin123456');
            console.log('\n🚀 Ready for production use with full upload capabilities!');
        } else {
            console.log('\n⚠️  Some upload features failed. Please check the errors above.');
        }
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    const tester = new UploadSystemTester();
    tester.runCompleteTest();
}

module.exports = UploadSystemTester;
