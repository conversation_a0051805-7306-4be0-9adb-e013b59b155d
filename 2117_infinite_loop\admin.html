<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Infinite Loop - Admin Panel</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="fontawesome-5.5/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 0.5rem 1.5rem;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .form-control {
            border-radius: 10px;
            border: 1px solid #e0e0e0;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        .spinner-border {
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-cogs"></i> Admin Panel</h1>
                    <p class="mb-0">Infinite Loop Website Management</p>
                </div>
                <div class="col-md-6 text-right">
                    <a href="/" class="btn btn-light">
                        <i class="fas fa-home"></i> Back to Website
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Statistics Row -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-comments fa-2x mb-2"></i>
                    <h3 id="testimonials-count">-</h3>
                    <p>Testimonials</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-images fa-2x mb-2"></i>
                    <h3 id="gallery-count">-</h3>
                    <p>Gallery Items</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-envelope fa-2x mb-2"></i>
                    <h3 id="messages-count">-</h3>
                    <p>Messages</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-cog fa-2x mb-2"></i>
                    <h3 id="services-count">-</h3>
                    <p>Services</p>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <ul class="nav nav-tabs" id="adminTabs" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="testimonials-tab" data-toggle="tab" href="#testimonials" role="tab">
                    <i class="fas fa-comments"></i> Testimonials
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="gallery-tab" data-toggle="tab" href="#gallery" role="tab">
                    <i class="fas fa-images"></i> Gallery
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="messages-tab" data-toggle="tab" href="#messages" role="tab">
                    <i class="fas fa-envelope"></i> Messages
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="services-tab" data-toggle="tab" href="#services" role="tab">
                    <i class="fas fa-cog"></i> Services
                </a>
            </li>
        </ul>

        <div class="tab-content" id="adminTabsContent">
            <!-- Testimonials Tab -->
            <div class="tab-pane fade show active" id="testimonials" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-comments"></i> Manage Testimonials</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-3" onclick="showAddTestimonialForm()">
                            <i class="fas fa-plus"></i> Add New Testimonial
                        </button>
                        <div id="testimonials-list" class="loading">
                            <div class="spinner-border" role="status"></div>
                            <p>Loading testimonials...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gallery Tab -->
            <div class="tab-pane fade" id="gallery" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-images"></i> Manage Gallery</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-3" onclick="showAddGalleryForm()">
                            <i class="fas fa-plus"></i> Add New Image
                        </button>
                        <div id="gallery-list" class="loading">
                            <div class="spinner-border" role="status"></div>
                            <p>Loading gallery...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Messages Tab -->
            <div class="tab-pane fade" id="messages" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-envelope"></i> Contact Messages</h5>
                    </div>
                    <div class="card-body">
                        <div id="messages-list" class="loading">
                            <div class="spinner-border" role="status"></div>
                            <p>Loading messages...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Services Tab -->
            <div class="tab-pane fade" id="services" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> Manage Services</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-3" onclick="showAddServiceForm()">
                            <i class="fas fa-plus"></i> Add New Service
                        </button>
                        <div id="services-list" class="loading">
                            <div class="spinner-border" role="status"></div>
                            <p>Loading services...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals will be added here dynamically -->
    <div id="modal-container"></div>

    <!-- Scripts -->
    <script src="js/jquery-1.9.1.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/api.js"></script>
    <script>
        // Admin Panel JavaScript
        class AdminPanel {
            constructor() {
                this.init();
            }

            async init() {
                await this.loadStatistics();
                await this.loadTestimonials();
                
                // Set up tab change handlers
                $('#adminTabs a').on('click', (e) => {
                    const target = $(e.target).attr('href');
                    this.handleTabChange(target);
                });
            }

            async handleTabChange(tab) {
                switch(tab) {
                    case '#gallery':
                        await this.loadGallery();
                        break;
                    case '#messages':
                        await this.loadMessages();
                        break;
                    case '#services':
                        await this.loadServices();
                        break;
                }
            }

            async loadStatistics() {
                try {
                    const [testimonials, gallery, services, contactStats] = await Promise.all([
                        api.getTestimonials(),
                        api.getGallery(),
                        api.getServices(),
                        api.getContactStatistics()
                    ]);

                    $('#testimonials-count').text(testimonials.data.length);
                    $('#gallery-count').text(gallery.data.length);
                    $('#services-count').text(services.data.length);
                    $('#messages-count').text(contactStats.data.total || 0);
                } catch (error) {
                    console.error('Error loading statistics:', error);
                }
            }

            async loadTestimonials() {
                try {
                    const response = await api.getTestimonials();
                    const testimonials = response.data;
                    
                    let html = '<div class="table-responsive"><table class="table table-striped">';
                    html += '<thead><tr><th>Name</th><th>Position</th><th>Rating</th><th>Actions</th></tr></thead><tbody>';
                    
                    testimonials.forEach(testimonial => {
                        html += `
                            <tr>
                                <td>${testimonial.name}</td>
                                <td>${testimonial.position}${testimonial.company ? ', ' + testimonial.company : ''}</td>
                                <td>${UIHelpers.generateStarRating(testimonial.rating)}</td>
                                <td>
                                    <button class="btn btn-sm btn-danger" onclick="deleteTestimonial(${testimonial.id})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table></div>';
                    $('#testimonials-list').html(html);
                } catch (error) {
                    $('#testimonials-list').html('<div class="alert alert-danger">Error loading testimonials</div>');
                }
            }

            async loadGallery() {
                try {
                    const response = await api.getGallery();
                    const gallery = response.data;
                    
                    let html = '<div class="row">';
                    
                    gallery.forEach(item => {
                        html += `
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <img src="${item.thumbnail_url || item.image_url}" class="card-img-top" style="height: 200px; object-fit: cover;">
                                    <div class="card-body">
                                        <h6 class="card-title">${item.title}</h6>
                                        <p class="card-text small">${item.description || ''}</p>
                                        <button class="btn btn-sm btn-danger" onclick="deleteGalleryItem(${item.id})">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    
                    html += '</div>';
                    $('#gallery-list').html(html);
                } catch (error) {
                    $('#gallery-list').html('<div class="alert alert-danger">Error loading gallery</div>');
                }
            }

            async loadMessages() {
                try {
                    const response = await api.get('/contact');
                    const messages = response.data;
                    
                    let html = '<div class="table-responsive"><table class="table table-striped">';
                    html += '<thead><tr><th>Name</th><th>Email</th><th>Subject</th><th>Date</th><th>Status</th><th>Actions</th></tr></thead><tbody>';
                    
                    messages.forEach(message => {
                        const statusBadge = message.status === 'new' ? 'badge-primary' : 
                                          message.status === 'read' ? 'badge-info' : 
                                          message.status === 'replied' ? 'badge-success' : 'badge-secondary';
                        
                        html += `
                            <tr>
                                <td>${message.name}</td>
                                <td>${message.email}</td>
                                <td>${message.subject || 'No subject'}</td>
                                <td>${UIHelpers.formatDate(message.created_at)}</td>
                                <td><span class="badge ${statusBadge}">${message.status}</span></td>
                                <td>
                                    <button class="btn btn-sm btn-info" onclick="viewMessage(${message.id})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteMessage(${message.id})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table></div>';
                    $('#messages-list').html(html);
                } catch (error) {
                    $('#messages-list').html('<div class="alert alert-danger">Error loading messages</div>');
                }
            }

            async loadServices() {
                try {
                    const response = await api.getServices();
                    const services = response.data;
                    
                    let html = '<div class="table-responsive"><table class="table table-striped">';
                    html += '<thead><tr><th>Title</th><th>Icon</th><th>Status</th><th>Order</th><th>Actions</th></tr></thead><tbody>';
                    
                    services.forEach(service => {
                        html += `
                            <tr>
                                <td>${service.title}</td>
                                <td><i class="${service.icon}"></i> ${service.icon}</td>
                                <td><span class="badge ${service.is_active ? 'badge-success' : 'badge-secondary'}">${service.is_active ? 'Active' : 'Inactive'}</span></td>
                                <td>${service.sort_order}</td>
                                <td>
                                    <button class="btn btn-sm btn-warning" onclick="toggleService(${service.id})">
                                        <i class="fas fa-toggle-${service.is_active ? 'on' : 'off'}"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteService(${service.id})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table></div>';
                    $('#services-list').html(html);
                } catch (error) {
                    $('#services-list').html('<div class="alert alert-danger">Error loading services</div>');
                }
            }
        }

        // Global functions for actions
        async function deleteTestimonial(id) {
            if (confirm('Are you sure you want to delete this testimonial?')) {
                try {
                    await api.delete(`/testimonials/${id}`);
                    adminPanel.loadTestimonials();
                    adminPanel.loadStatistics();
                } catch (error) {
                    alert('Error deleting testimonial');
                }
            }
        }

        async function deleteGalleryItem(id) {
            if (confirm('Are you sure you want to delete this gallery item?')) {
                try {
                    await api.delete(`/gallery/${id}`);
                    adminPanel.loadGallery();
                    adminPanel.loadStatistics();
                } catch (error) {
                    alert('Error deleting gallery item');
                }
            }
        }

        async function deleteMessage(id) {
            if (confirm('Are you sure you want to delete this message?')) {
                try {
                    await api.delete(`/contact/${id}`);
                    adminPanel.loadMessages();
                    adminPanel.loadStatistics();
                } catch (error) {
                    alert('Error deleting message');
                }
            }
        }

        async function deleteService(id) {
            if (confirm('Are you sure you want to delete this service?')) {
                try {
                    await api.delete(`/services/${id}`);
                    adminPanel.loadServices();
                    adminPanel.loadStatistics();
                } catch (error) {
                    alert('Error deleting service');
                }
            }
        }

        async function toggleService(id) {
            try {
                await api.put(`/services/${id}/toggle-status`, {});
                adminPanel.loadServices();
            } catch (error) {
                alert('Error toggling service status');
            }
        }

        function showAddTestimonialForm() {
            alert('Add testimonial form - To be implemented');
        }

        function showAddGalleryForm() {
            alert('Add gallery form - To be implemented');
        }

        function showAddServiceForm() {
            alert('Add service form - To be implemented');
        }

        function viewMessage(id) {
            alert('View message - To be implemented');
        }

        // Initialize admin panel
        let adminPanel;
        $(document).ready(() => {
            adminPanel = new AdminPanel();
        });
    </script>
</body>
</html>
