<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الرئيسية - ELAshrafy</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="fontawesome-5.5/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        
        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }
        
        .sidebar.collapsed {
            width: 70px;
        }
        
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar.collapsed .sidebar-header .brand-text {
            display: none;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }
        
        .sidebar-menu li {
            margin: 5px 0;
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 0 25px 25px 0;
            margin-left: 10px;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .sidebar-menu i {
            width: 20px;
            margin-left: 15px;
            text-align: center;
        }
        
        .sidebar.collapsed .sidebar-menu .menu-text {
            display: none;
        }
        
        /* Main Content */
        .main-content {
            margin-right: 280px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }
        
        .main-content.expanded {
            margin-right: 70px;
        }
        
        /* Top Bar */
        .top-bar {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .toggle-sidebar {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #667eea;
            cursor: pointer;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        /* Dashboard Cards */
        .dashboard-content {
            padding: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 15px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        /* Charts and Tables */
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .content-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .content-card h3 {
            margin-bottom: 20px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }
        
        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .action-btn i {
            font-size: 2rem;
        }
        
        /* Recent Activity */
        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-size: 0.9rem;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .activity-time {
            font-size: 0.8rem;
            color: #666;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
        
        /* Loading States */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #667eea;
        }
        
        .spinner {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Alerts */
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 20px;
        }
        
        .alert-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }
        
        .alert-warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: white;
        }
        
        /* System Status */
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-online {
            background: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }
        
        .status-offline {
            background: #dc3545;
            box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="brand-text">
                <h3><i class="fas fa-crown"></i> ELAshrafy</h3>
                <p class="mb-0">لوحة التحكم الرئيسية</p>
            </div>
        </div>
        
        <ul class="sidebar-menu">
            <li>
                <a href="#dashboard" class="menu-item active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="menu-text">الرئيسية</span>
                </a>
            </li>
            <li>
                <a href="#users" class="menu-item" data-section="users">
                    <i class="fas fa-users"></i>
                    <span class="menu-text">إدارة المستخدمين</span>
                </a>
            </li>
            <li>
                <a href="#testimonials" class="menu-item" data-section="testimonials">
                    <i class="fas fa-comments"></i>
                    <span class="menu-text">الشهادات</span>
                </a>
            </li>
            <li>
                <a href="#gallery" class="menu-item" data-section="gallery">
                    <i class="fas fa-images"></i>
                    <span class="menu-text">المعرض</span>
                </a>
            </li>
            <li>
                <a href="#services" class="menu-item" data-section="services">
                    <i class="fas fa-cogs"></i>
                    <span class="menu-text">الخدمات</span>
                </a>
            </li>
            <li>
                <a href="#messages" class="menu-item" data-section="messages">
                    <i class="fas fa-envelope"></i>
                    <span class="menu-text">الرسائل</span>
                </a>
            </li>
            <li>
                <a href="#analytics" class="menu-item" data-section="analytics">
                    <i class="fas fa-chart-bar"></i>
                    <span class="menu-text">الإحصائيات</span>
                </a>
            </li>
            <li>
                <a href="#settings" class="menu-item" data-section="settings">
                    <i class="fas fa-cog"></i>
                    <span class="menu-text">الإعدادات</span>
                </a>
            </li>
            <li>
                <a href="#system" class="menu-item" data-section="system">
                    <i class="fas fa-server"></i>
                    <span class="menu-text">النظام</span>
                </a>
            </li>
        </ul>
    </div>
    
    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="d-flex align-items-center">
                <button class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h4 class="mb-0 mr-3" id="pageTitle">لوحة التحكم الرئيسية</h4>
                <span class="status-indicator status-online" title="النظام متصل"></span>
            </div>
            
            <div class="user-info">
                <div class="text-right">
                    <div class="font-weight-bold" id="userName">المدير</div>
                    <small class="text-muted" id="userRole">مدير النظام</small>
                </div>
                <div class="user-avatar" id="userAvatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="dropdown">
                    <button class="btn btn-link text-dark" data-toggle="dropdown">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="dropdown-menu dropdown-menu-left">
                        <a class="dropdown-item" href="#" onclick="showProfile()">
                            <i class="fas fa-user"></i> الملف الشخصي
                        </a>
                        <a class="dropdown-item" href="#" onclick="changePassword()">
                            <i class="fas fa-key"></i> تغيير كلمة المرور
                        </a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="/" target="_blank">
                            <i class="fas fa-external-link-alt"></i> عرض الموقع
                        </a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item text-danger" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Alert Container -->
            <div id="alertContainer"></div>
            
            <!-- Content Sections -->
            <div id="contentContainer">
                <!-- Dashboard Section will be loaded here -->
            </div>
        </div>
    </div>
    
    <!-- Modals Container -->
    <div id="modalContainer"></div>
    
    <!-- Scripts -->
    <script src="js/jquery-1.9.1.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/api.js"></script>
    
    <script>
        // Dashboard Manager Class
        class DashboardManager {
            constructor() {
                this.currentUser = null;
                this.currentSection = 'dashboard';
                this.init();
            }
            
            async init() {
                // Check authentication
                if (!(await this.checkAuth())) {
                    return;
                }
                
                // Initialize UI
                this.initializeUI();
                
                // Load dashboard
                await this.loadSection('dashboard');
            }
            
            async checkAuth() {
                try {
                    const response = await api.get('/auth/me');
                    if (response.success) {
                        this.currentUser = response.data.user;
                        this.updateUserInfo();
                        return true;
                    }
                } catch (error) {
                    console.log('Authentication failed, redirecting...');
                }
                
                window.location.href = '/login.html';
                return false;
            }
            
            updateUserInfo() {
                if (this.currentUser) {
                    document.getElementById('userName').textContent = this.currentUser.full_name;
                    document.getElementById('userRole').textContent = 
                        this.currentUser.role === 'admin' ? 'مدير النظام' : 'مستخدم';
                    
                    // Update avatar with first letter of name
                    const firstLetter = this.currentUser.full_name.charAt(0).toUpperCase();
                    document.getElementById('userAvatar').innerHTML = firstLetter;
                }
            }
            
            initializeUI() {
                // Menu click handlers
                document.querySelectorAll('.menu-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        const section = item.dataset.section;
                        this.loadSection(section);
                        
                        // Update active menu
                        document.querySelectorAll('.menu-item').forEach(mi => mi.classList.remove('active'));
                        item.classList.add('active');
                    });
                });
            }
            
            async loadSection(section) {
                this.currentSection = section;
                const container = document.getElementById('contentContainer');
                const titleElement = document.getElementById('pageTitle');
                
                // Show loading
                container.innerHTML = '<div class="loading"><i class="fas fa-spinner spinner"></i> جاري التحميل...</div>';
                
                try {
                    let content = '';
                    let title = '';
                    
                    switch(section) {
                        case 'dashboard':
                            title = 'لوحة التحكم الرئيسية';
                            content = await this.getDashboardContent();
                            break;
                        case 'users':
                            title = 'إدارة المستخدمين';
                            content = await this.getUsersContent();
                            break;
                        case 'testimonials':
                            title = 'إدارة الشهادات';
                            content = await this.getTestimonialsContent();
                            break;
                        case 'gallery':
                            title = 'إدارة المعرض';
                            content = await this.getGalleryContent();
                            break;
                        case 'services':
                            title = 'إدارة الخدمات';
                            content = await this.getServicesContent();
                            break;
                        case 'messages':
                            title = 'إدارة الرسائل';
                            content = await this.getMessagesContent();
                            break;
                        case 'analytics':
                            title = 'الإحصائيات والتقارير';
                            content = await this.getAnalyticsContent();
                            break;
                        case 'settings':
                            title = 'إعدادات النظام';
                            content = await this.getSettingsContent();
                            break;
                        case 'system':
                            title = 'معلومات النظام';
                            content = await this.getSystemContent();
                            break;
                        default:
                            content = '<div class="alert alert-warning">القسم غير موجود</div>';
                    }
                    
                    titleElement.textContent = title;
                    container.innerHTML = content;
                    
                    // Initialize section-specific functionality
                    this.initializeSectionFeatures(section);
                    
                } catch (error) {
                    console.error('Error loading section:', error);
                    container.innerHTML = '<div class="alert alert-danger">حدث خطأ أثناء تحميل البيانات</div>';
                }
            }
            
            async getUsersContent() {
                const users = await api.get('/auth/users').catch(() => ({data: []}));
                const userStats = await api.get('/auth/statistics').catch(() => ({data: {total: 0, active: 0, admins: 0}}));

                return `
                    <!-- Users Statistics -->
                    <div class="stats-grid mb-4">
                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-number">${userStats.data.total || 0}</div>
                            <div class="stat-label">إجمالي المستخدمين</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="stat-number">${userStats.data.active || 0}</div>
                            <div class="stat-label">المستخدمين النشطين</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <div class="stat-number">${userStats.data.admins || 0}</div>
                            <div class="stat-label">المديرين</div>
                        </div>
                    </div>

                    <!-- Users Management -->
                    <div class="content-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3><i class="fas fa-users"></i> إدارة المستخدمين</h3>
                            <button class="btn btn-primary" onclick="showAddUserModal()">
                                <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
                            </button>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم الكامل</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>اسم المستخدم</th>
                                        <th>الدور</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>آخر دخول</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${users.data.map(user => `
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="user-avatar mr-2" style="width: 30px; height: 30px; font-size: 0.8rem;">
                                                        ${user.full_name.charAt(0).toUpperCase()}
                                                    </div>
                                                    ${user.full_name}
                                                </div>
                                            </td>
                                            <td>${user.email}</td>
                                            <td>${user.username}</td>
                                            <td>
                                                <span class="badge ${user.role === 'admin' ? 'badge-danger' : 'badge-primary'}">
                                                    ${user.role === 'admin' ? 'مدير' : 'مستخدم'}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge ${user.is_active ? 'badge-success' : 'badge-secondary'}">
                                                    ${user.is_active ? 'نشط' : 'غير نشط'}
                                                </span>
                                            </td>
                                            <td>${new Date(user.created_at).toLocaleDateString('ar-SA')}</td>
                                            <td>${user.last_login ? new Date(user.last_login).toLocaleDateString('ar-SA') : 'لم يدخل بعد'}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="editUser(${user.id})" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning" onclick="toggleUserStatus(${user.id})" title="تغيير الحالة">
                                                        <i class="fas fa-toggle-${user.is_active ? 'on' : 'off'}"></i>
                                                    </button>
                                                    ${user.id !== this.currentUser.id ? `
                                                        <button class="btn btn-outline-danger" onclick="deleteUser(${user.id})" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    ` : ''}
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
            }

            async getDashboardContent() {
                // Get statistics
                const [testimonials, gallery, services, contactStats, userStats] = await Promise.all([
                    api.getTestimonials().catch(() => ({data: []})),
                    api.getGallery().catch(() => ({data: []})),
                    api.getServices().catch(() => ({data: []})),
                    api.get('/contact/statistics').catch(() => ({data: {total: 0}})),
                    api.get('/auth/statistics').catch(() => ({data: {total: 0}}))
                ]);
                
                return `
                    <!-- Welcome Alert -->
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        مرحباً بك في لوحة التحكم الرئيسية لمنصة ELAshrafy. من هنا يمكنك إدارة جميع جوانب النظام.
                    </div>
                    
                    <!-- Statistics Cards -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-number">${userStats.data.total || 0}</div>
                            <div class="stat-label">إجمالي المستخدمين</div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);">
                                <i class="fas fa-comments"></i>
                            </div>
                            <div class="stat-number">${testimonials.data.length}</div>
                            <div class="stat-label">الشهادات والتوصيات</div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);">
                                <i class="fas fa-images"></i>
                            </div>
                            <div class="stat-number">${gallery.data.length}</div>
                            <div class="stat-label">صور المعرض</div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="stat-number">${contactStats.data.total || 0}</div>
                            <div class="stat-label">رسائل التواصل</div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="stat-number">${services.data.length}</div>
                            <div class="stat-label">الخدمات المتاحة</div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-number">100%</div>
                            <div class="stat-label">حالة النظام</div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <h4><i class="fas fa-bolt"></i> إجراءات سريعة</h4>
                    <div class="quick-actions">
                        <a href="#" class="action-btn" onclick="dashboard.loadSection('users')">
                            <i class="fas fa-user-plus"></i>
                            <span>إضافة مستخدم</span>
                        </a>
                        <a href="#" class="action-btn" onclick="dashboard.loadSection('testimonials')">
                            <i class="fas fa-comment-plus"></i>
                            <span>إضافة شهادة</span>
                        </a>
                        <a href="#" class="action-btn" onclick="dashboard.loadSection('gallery')">
                            <i class="fas fa-image"></i>
                            <span>إضافة صورة</span>
                        </a>
                        <a href="#" class="action-btn" onclick="dashboard.loadSection('services')">
                            <i class="fas fa-plus-circle"></i>
                            <span>إضافة خدمة</span>
                        </a>
                        <a href="#" class="action-btn" onclick="dashboard.loadSection('messages')">
                            <i class="fas fa-envelope-open"></i>
                            <span>عرض الرسائل</span>
                        </a>
                        <a href="/" target="_blank" class="action-btn">
                            <i class="fas fa-external-link-alt"></i>
                            <span>عرض الموقع</span>
                        </a>
                    </div>
                    
                    <!-- Content Grid -->
                    <div class="content-grid">
                        <div class="content-card">
                            <h3><i class="fas fa-chart-area"></i> نظرة عامة على النشاط</h3>
                            <div id="activityChart">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-chart-line fa-3x mb-3"></i>
                                    <p>سيتم عرض الرسوم البيانية هنا</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="content-card">
                            <h3><i class="fas fa-clock"></i> النشاط الأخير</h3>
                            <div id="recentActivity">
                                <div class="activity-item">
                                    <div class="activity-icon" style="background: #28a745;">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">تم إنشاء حساب جديد</div>
                                        <div class="activity-time">منذ ساعتين</div>
                                    </div>
                                </div>
                                
                                <div class="activity-item">
                                    <div class="activity-icon" style="background: #007bff;">
                                        <i class="fas fa-comment"></i>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">شهادة جديدة تمت إضافتها</div>
                                        <div class="activity-time">منذ 3 ساعات</div>
                                    </div>
                                </div>
                                
                                <div class="activity-item">
                                    <div class="activity-icon" style="background: #fd7e14;">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">رسالة تواصل جديدة</div>
                                        <div class="activity-time">منذ 5 ساعات</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            async getTestimonialsContent() {
                const testimonials = await api.getTestimonials().catch(() => ({data: []}));

                return `
                    <div class="content-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3><i class="fas fa-comments"></i> إدارة الشهادات والتوصيات</h3>
                            <button class="btn btn-primary" onclick="showAddTestimonialModal()">
                                <i class="fas fa-plus"></i> إضافة شهادة جديدة
                            </button>
                        </div>

                        <div class="row">
                            ${testimonials.data.map(testimonial => `
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-3">
                                                <img src="${testimonial.image_url || 'https://via.placeholder.com/50'}"
                                                     class="rounded-circle mr-3" width="50" height="50" alt="${testimonial.name}">
                                                <div>
                                                    <h6 class="mb-0">${testimonial.name}</h6>
                                                    <small class="text-muted">${testimonial.position}${testimonial.company ? ', ' + testimonial.company : ''}</small>
                                                </div>
                                            </div>
                                            <p class="card-text">${testimonial.message.substring(0, 100)}...</p>
                                            <div class="mb-3">
                                                ${Array.from({length: 5}, (_, i) =>
                                                    `<i class="fas fa-star ${i < testimonial.rating ? 'text-warning' : 'text-muted'}"></i>`
                                                ).join('')}
                                            </div>
                                        </div>
                                        <div class="card-footer">
                                            <div class="btn-group btn-group-sm w-100">
                                                <button class="btn btn-outline-primary" onclick="editTestimonial(${testimonial.id})">
                                                    <i class="fas fa-edit"></i> تعديل
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteTestimonial(${testimonial.id})">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }

            async getGalleryContent() {
                const gallery = await api.getGallery().catch(() => ({data: []}));
                const categories = await api.get('/gallery/categories').catch(() => ({data: []}));

                return `
                    <div class="content-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3><i class="fas fa-images"></i> إدارة معرض الصور</h3>
                            <button class="btn btn-primary" onclick="showAddGalleryModal()">
                                <i class="fas fa-plus"></i> إضافة صورة جديدة
                            </button>
                        </div>

                        <div class="mb-3">
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-secondary active" onclick="filterGallery('all')">الكل</button>
                                ${categories.data.map(category => `
                                    <button class="btn btn-outline-secondary" onclick="filterGallery('${category}')">${category}</button>
                                `).join('')}
                            </div>
                        </div>

                        <div class="row" id="galleryGrid">
                            ${gallery.data.map(item => `
                                <div class="col-md-4 col-lg-3 mb-4 gallery-item" data-category="${item.category}">
                                    <div class="card">
                                        <img src="${item.thumbnail_url || item.image_url}" class="card-img-top" style="height: 200px; object-fit: cover;" alt="${item.title}">
                                        <div class="card-body p-3">
                                            <h6 class="card-title mb-1">${item.title}</h6>
                                            <small class="text-muted">${item.category}</small>
                                            <p class="card-text small mt-2">${item.description || ''}</p>
                                        </div>
                                        <div class="card-footer p-2">
                                            <div class="btn-group btn-group-sm w-100">
                                                <button class="btn btn-outline-primary" onclick="editGalleryItem(${item.id})">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info" onclick="viewGalleryItem('${item.image_url}')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteGalleryItem(${item.id})">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }

            async getMessagesContent() {
                const messages = await api.get('/contact').catch(() => ({data: []}));
                const stats = await api.get('/contact/statistics').catch(() => ({data: {}}));

                return `
                    <!-- Messages Statistics -->
                    <div class="stats-grid mb-4">
                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="stat-number">${stats.data.total || 0}</div>
                            <div class="stat-label">إجمالي الرسائل</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                                <i class="fas fa-envelope-open"></i>
                            </div>
                            <div class="stat-number">${stats.data.byStatus?.read || 0}</div>
                            <div class="stat-label">الرسائل المقروءة</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);">
                                <i class="fas fa-envelope-square"></i>
                            </div>
                            <div class="stat-number">${stats.data.byStatus?.new || 0}</div>
                            <div class="stat-label">رسائل جديدة</div>
                        </div>
                    </div>

                    <div class="content-card">
                        <h3><i class="fas fa-envelope"></i> إدارة رسائل التواصل</h3>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الموضوع</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${messages.data.map(message => `
                                        <tr class="${message.status === 'new' ? 'table-warning' : ''}">
                                            <td>${message.name}</td>
                                            <td>${message.email}</td>
                                            <td>${message.subject || 'بدون موضوع'}</td>
                                            <td>${new Date(message.created_at).toLocaleDateString('ar-SA')}</td>
                                            <td>
                                                <span class="badge ${
                                                    message.status === 'new' ? 'badge-warning' :
                                                    message.status === 'read' ? 'badge-info' :
                                                    message.status === 'replied' ? 'badge-success' : 'badge-secondary'
                                                }">
                                                    ${
                                                        message.status === 'new' ? 'جديد' :
                                                        message.status === 'read' ? 'مقروء' :
                                                        message.status === 'replied' ? 'تم الرد' : 'مؤرشف'
                                                    }
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="viewMessage(${message.id})" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="replyMessage(${message.id})" title="رد">
                                                        <i class="fas fa-reply"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="deleteMessage(${message.id})" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
            }

            async getSystemContent() {
                return `
                    <div class="content-card">
                        <h3><i class="fas fa-server"></i> معلومات النظام</h3>

                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-info-circle"></i> معلومات عامة</h5>
                                <table class="table table-sm">
                                    <tr><td><strong>اسم النظام:</strong></td><td>ELAshrafy Platform</td></tr>
                                    <tr><td><strong>الإصدار:</strong></td><td>1.0.0</td></tr>
                                    <tr><td><strong>حالة النظام:</strong></td><td><span class="badge badge-success">متصل</span></td></tr>
                                    <tr><td><strong>وقت التشغيل:</strong></td><td id="uptime">جاري الحساب...</td></tr>
                                    <tr><td><strong>قاعدة البيانات:</strong></td><td>SQLite</td></tr>
                                </table>
                            </div>

                            <div class="col-md-6">
                                <h5><i class="fas fa-chart-pie"></i> استخدام الموارد</h5>
                                <div class="mb-3">
                                    <label>استخدام المعالج</label>
                                    <div class="progress">
                                        <div class="progress-bar bg-success" style="width: 25%">25%</div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label>استخدام الذاكرة</label>
                                    <div class="progress">
                                        <div class="progress-bar bg-info" style="width: 45%">45%</div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label>مساحة القرص</label>
                                    <div class="progress">
                                        <div class="progress-bar bg-warning" style="width: 60%">60%</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-md-12">
                                <h5><i class="fas fa-tools"></i> أدوات النظام</h5>
                                <div class="btn-group mb-3">
                                    <button class="btn btn-outline-primary" onclick="backupDatabase()">
                                        <i class="fas fa-download"></i> نسخ احتياطي لقاعدة البيانات
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="clearCache()">
                                        <i class="fas fa-broom"></i> مسح الذاكرة المؤقتة
                                    </button>
                                    <button class="btn btn-outline-info" onclick="checkUpdates()">
                                        <i class="fas fa-sync"></i> فحص التحديثات
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>ملاحظة:</strong> يتم تحديث معلومات النظام تلقائياً كل 30 ثانية.
                        </div>
                    </div>
                `;
            }

            async getServicesContent() {
                const services = await api.getServices().catch(() => ({data: []}));

                return `
                    <div class="content-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3><i class="fas fa-cogs"></i> إدارة الخدمات</h3>
                            <button class="btn btn-primary" onclick="showAddServiceModal()">
                                <i class="fas fa-plus"></i> إضافة خدمة جديدة
                            </button>
                        </div>

                        <div class="row">
                            ${services.data.map(service => `
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <div class="service-icon mb-3">
                                                <i class="${service.icon} fa-3x text-primary"></i>
                                            </div>
                                            <h5 class="card-title">${service.title}</h5>
                                            <p class="card-text">${service.description}</p>
                                            <div class="mt-3">
                                                <span class="badge ${service.is_active ? 'badge-success' : 'badge-secondary'}">
                                                    ${service.is_active ? 'نشط' : 'غير نشط'}
                                                </span>
                                                <span class="badge badge-info">ترتيب: ${service.sort_order}</span>
                                            </div>
                                        </div>
                                        <div class="card-footer">
                                            <div class="btn-group btn-group-sm w-100">
                                                <button class="btn btn-outline-primary" onclick="editService(${service.id})">
                                                    <i class="fas fa-edit"></i> تعديل
                                                </button>
                                                <button class="btn btn-outline-warning" onclick="toggleService(${service.id})">
                                                    <i class="fas fa-toggle-${service.is_active ? 'on' : 'off'}"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteService(${service.id})">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }

            async getAnalyticsContent() {
                return `
                    <div class="content-card">
                        <h3><i class="fas fa-chart-bar"></i> الإحصائيات والتقارير</h3>

                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                                        <i class="fas fa-eye"></i>
                                    </div>
                                    <div class="stat-number">12,543</div>
                                    <div class="stat-label">زيارات الموقع</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-icon" style="background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="stat-number">3,247</div>
                                    <div class="stat-label">زوار فريدون</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-icon" style="background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="stat-number">4:32</div>
                                    <div class="stat-label">متوسط وقت الزيارة</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-icon" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);">
                                        <i class="fas fa-percentage"></i>
                                    </div>
                                    <div class="stat-number">68%</div>
                                    <div class="stat-label">معدل الارتداد</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <h5>إحصائيات الزيارات (آخر 30 يوم)</h5>
                                <div class="text-center py-5">
                                    <i class="fas fa-chart-line fa-4x text-muted mb-3"></i>
                                    <p class="text-muted">سيتم عرض الرسوم البيانية هنا</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h5>أهم الصفحات</h5>
                                <div class="list-group">
                                    <div class="list-group-item d-flex justify-content-between">
                                        <span>الصفحة الرئيسية</span>
                                        <span class="badge badge-primary">45%</span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between">
                                        <span>المعرض</span>
                                        <span class="badge badge-primary">23%</span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between">
                                        <span>الخدمات</span>
                                        <span class="badge badge-primary">18%</span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between">
                                        <span>التواصل</span>
                                        <span class="badge badge-primary">14%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            async getSettingsContent() {
                return `
                    <div class="content-card">
                        <h3><i class="fas fa-cog"></i> إعدادات الموقع</h3>

                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-home"></i> إعدادات الصفحة الرئيسية</h5>
                                <form id="homeSettingsForm">
                                    <div class="form-group">
                                        <label>عنوان الموقع</label>
                                        <input type="text" class="form-control" name="site_title" value="ELAshrafy">
                                    </div>
                                    <div class="form-group">
                                        <label>العنوان الفرعي</label>
                                        <input type="text" class="form-control" name="site_subtitle" value="Professional Website Platform">
                                    </div>
                                    <div class="form-group">
                                        <label>وصف الموقع</label>
                                        <textarea class="form-control" name="site_description" rows="3">منصة إدارة المحتوى الاحترافية</textarea>
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="saveHomeSettings()">
                                        <i class="fas fa-save"></i> حفظ الإعدادات
                                    </button>
                                </form>
                            </div>

                            <div class="col-md-6">
                                <h5><i class="fas fa-envelope"></i> إعدادات التواصل</h5>
                                <form id="contactSettingsForm">
                                    <div class="form-group">
                                        <label>البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="contact_email" value="<EMAIL>">
                                    </div>
                                    <div class="form-group">
                                        <label>رقم الهاتف</label>
                                        <input type="text" class="form-control" name="contact_phone" value="+966 50 123 4567">
                                    </div>
                                    <div class="form-group">
                                        <label>العنوان</label>
                                        <textarea class="form-control" name="contact_address" rows="3">الرياض، المملكة العربية السعودية</textarea>
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="saveContactSettings()">
                                        <i class="fas fa-save"></i> حفظ الإعدادات
                                    </button>
                                </form>
                            </div>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-md-12">
                                <h5><i class="fas fa-palette"></i> إعدادات التصميم</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>اللون الأساسي</label>
                                            <input type="color" class="form-control" name="primary_color" value="#667eea">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>اللون الثانوي</label>
                                            <input type="color" class="form-control" name="secondary_color" value="#764ba2">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>لون النص</label>
                                            <input type="color" class="form-control" name="text_color" value="#333333">
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary" onclick="saveDesignSettings()">
                                    <i class="fas fa-save"></i> حفظ إعدادات التصميم
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }

            initializeSectionFeatures(section) {
                // Initialize section-specific features here
                switch(section) {
                    case 'dashboard':
                        // Initialize dashboard features
                        break;
                    case 'system':
                        this.updateSystemUptime();
                        break;
                    // Add other sections as needed
                }
            }

            updateSystemUptime() {
                // Update uptime every second
                const startTime = Date.now();
                setInterval(() => {
                    const uptime = Date.now() - startTime;
                    const seconds = Math.floor(uptime / 1000);
                    const minutes = Math.floor(seconds / 60);
                    const hours = Math.floor(minutes / 60);

                    const uptimeElement = document.getElementById('uptime');
                    if (uptimeElement) {
                        uptimeElement.textContent = `${hours}:${minutes % 60}:${seconds % 60}`;
                    }
                }, 1000);
            }
        }
        
        // Global functions
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        }
        
        async function logout() {
            try {
                await api.post('/auth/logout', {});
                localStorage.removeItem('user');
                localStorage.removeItem('token');
                window.location.href = '/login.html';
            } catch (error) {
                console.error('Logout error:', error);
                window.location.href = '/login.html';
            }
        }
        
        function showProfile() {
            alert('صفحة الملف الشخصي قيد التطوير');
        }
        
        function changePassword() {
            alert('صفحة تغيير كلمة المرور قيد التطوير');
        }

        // File Upload Functions
        async function uploadImage(file, type = 'temp') {
            const formData = new FormData();
            formData.append('image', file);
            formData.append('type', type);

            try {
                const response = await fetch('/api/upload/image', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: formData
                });

                const result = await response.json();
                if (result.success) {
                    return result.data;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                throw new Error('فشل في رفع الصورة: ' + error.message);
            }
        }

        // User Management Functions
        function showAddUserModal() {
            const modalHTML = `
                <div class="modal fade" id="addUserModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">إضافة مستخدم جديد</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <form id="addUserForm">
                                    <div class="form-group">
                                        <label>الاسم الكامل</label>
                                        <input type="text" class="form-control" name="full_name" required>
                                    </div>
                                    <div class="form-group">
                                        <label>اسم المستخدم</label>
                                        <input type="text" class="form-control" name="username" required>
                                    </div>
                                    <div class="form-group">
                                        <label>البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="email" required>
                                    </div>
                                    <div class="form-group">
                                        <label>كلمة المرور</label>
                                        <input type="password" class="form-control" name="password" required>
                                    </div>
                                    <div class="form-group">
                                        <label>الدور</label>
                                        <select class="form-control" name="role">
                                            <option value="user">مستخدم</option>
                                            <option value="admin">مدير</option>
                                        </select>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="createUser()">إضافة</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalContainer').innerHTML = modalHTML;
            $('#addUserModal').modal('show');
        }

        async function createUser() {
            const form = document.getElementById('addUserForm');
            const formData = new FormData(form);
            const userData = Object.fromEntries(formData.entries());

            try {
                const response = await api.post('/auth/register', userData);
                if (response.success) {
                    $('#addUserModal').modal('hide');
                    dashboard.loadSection('users');
                    showAlert('تم إضافة المستخدم بنجاح', 'success');
                } else {
                    showAlert(response.message || 'فشل في إضافة المستخدم', 'danger');
                }
            } catch (error) {
                showAlert('حدث خطأ أثناء إضافة المستخدم', 'danger');
            }
        }

        function editUser(userId) {
            // Get user data first
            api.get(`/auth/users/${userId}`).then(response => {
                if (response.success) {
                    const user = response.data;
                    showEditUserModal(user);
                } else {
                    showAlert('فشل في جلب بيانات المستخدم', 'danger');
                }
            }).catch(error => {
                showAlert('حدث خطأ أثناء جلب بيانات المستخدم', 'danger');
            });
        }

        function showEditUserModal(user) {
            const modalHTML = `
                <div class="modal fade" id="editUserModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">تعديل المستخدم</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <form id="editUserForm">
                                    <input type="hidden" name="id" value="${user.id}">
                                    <div class="form-group">
                                        <label>الاسم الكامل</label>
                                        <input type="text" class="form-control" name="full_name" value="${user.full_name || ''}" required>
                                    </div>
                                    <div class="form-group">
                                        <label>اسم المستخدم</label>
                                        <input type="text" class="form-control" name="username" value="${user.username}" required>
                                    </div>
                                    <div class="form-group">
                                        <label>البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="email" value="${user.email}" required>
                                    </div>
                                    <div class="form-group">
                                        <label>كلمة المرور الجديدة (اتركها فارغة إذا لم تريد تغييرها)</label>
                                        <input type="password" class="form-control" name="password" placeholder="كلمة مرور جديدة">
                                    </div>
                                    <div class="form-group">
                                        <label>الدور</label>
                                        <select class="form-control" name="role">
                                            <option value="user" ${user.role === 'user' ? 'selected' : ''}>مستخدم</option>
                                            <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>مدير</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" name="is_active" ${user.is_active ? 'checked' : ''}>
                                            <label class="form-check-label">حساب نشط</label>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="updateUser()" id="updateUserBtn">
                                    <span class="btn-text">حفظ التغييرات</span>
                                    <span class="btn-loading" style="display: none;">
                                        <i class="fas fa-spinner fa-spin"></i> جاري الحفظ...
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalContainer').innerHTML = modalHTML;
            $('#editUserModal').modal('show');
        }

        async function updateUser() {
            const form = document.getElementById('editUserForm');
            const formData = new FormData(form);
            const userData = Object.fromEntries(formData.entries());
            const btn = document.getElementById('updateUserBtn');

            // Convert checkbox to boolean
            userData.is_active = formData.has('is_active');

            // Remove password if empty
            if (!userData.password) {
                delete userData.password;
            }

            try {
                // Show loading
                btn.querySelector('.btn-text').style.display = 'none';
                btn.querySelector('.btn-loading').style.display = 'inline';
                btn.disabled = true;

                const response = await api.put(`/auth/users/${userData.id}`, userData);
                if (response.success) {
                    $('#editUserModal').modal('hide');
                    dashboard.loadSection('users');
                    showAlert('تم تحديث المستخدم بنجاح', 'success');
                } else {
                    showAlert(response.message || 'فشل في تحديث المستخدم', 'danger');
                }
            } catch (error) {
                showAlert('حدث خطأ أثناء تحديث المستخدم', 'danger');
            } finally {
                // Hide loading
                btn.querySelector('.btn-text').style.display = 'inline';
                btn.querySelector('.btn-loading').style.display = 'none';
                btn.disabled = false;
            }
        }

        async function toggleUserStatus(userId) {
            try {
                const response = await api.put(`/auth/users/${userId}/toggle-status`, {});
                if (response.success) {
                    dashboard.loadSection('users');
                    showAlert('تم تحديث حالة المستخدم', 'success');
                } else {
                    showAlert(response.message || 'فشل في تحديث حالة المستخدم', 'danger');
                }
            } catch (error) {
                showAlert('حدث خطأ أثناء تحديث حالة المستخدم', 'danger');
            }
        }

        async function deleteUser(userId) {
            // Enhanced confirmation dialog
            const confirmModal = `
                <div class="modal fade" id="confirmDeleteModal" tabindex="-1">
                    <div class="modal-dialog modal-sm">
                        <div class="modal-content">
                            <div class="modal-header bg-danger text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-exclamation-triangle"></i> تأكيد الحذف
                                </h5>
                            </div>
                            <div class="modal-body text-center">
                                <i class="fas fa-user-times fa-3x text-danger mb-3"></i>
                                <p><strong>هل أنت متأكد من حذف هذا المستخدم؟</strong></p>
                                <p class="text-muted">لا يمكن التراجع عن هذا الإجراء</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-danger" onclick="confirmDeleteUser(${userId})" id="confirmDeleteBtn">
                                    <i class="fas fa-trash"></i> حذف نهائي
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalContainer').innerHTML = confirmModal;
            $('#confirmDeleteModal').modal('show');
        }

        async function confirmDeleteUser(userId) {
            const btn = document.getElementById('confirmDeleteBtn');

            try {
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحذف...';
                btn.disabled = true;

                const response = await api.delete(`/auth/users/${userId}`);
                if (response.success) {
                    $('#confirmDeleteModal').modal('hide');
                    dashboard.loadSection('users');
                    showAlert('تم حذف المستخدم بنجاح', 'success');
                } else {
                    showAlert(response.message || 'فشل في حذف المستخدم', 'danger');
                }
            } catch (error) {
                showAlert('حدث خطأ أثناء حذف المستخدم', 'danger');
            } finally {
                btn.innerHTML = '<i class="fas fa-trash"></i> حذف نهائي';
                btn.disabled = false;
            }
        }

        // Testimonial Functions
        function showAddTestimonialModal() {
            const modalHTML = `
                <div class="modal fade" id="addTestimonialModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">إضافة شهادة جديدة</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <form id="addTestimonialForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>اسم العميل *</label>
                                                <input type="text" class="form-control" name="name" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>المنصب *</label>
                                                <input type="text" class="form-control" name="position" required>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>الشركة</label>
                                                <input type="text" class="form-control" name="company">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>التقييم</label>
                                                <select class="form-control" name="rating">
                                                    <option value="5">5 نجوم</option>
                                                    <option value="4">4 نجوم</option>
                                                    <option value="3">3 نجوم</option>
                                                    <option value="2">2 نجوم</option>
                                                    <option value="1">1 نجمة</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label>نص الشهادة *</label>
                                        <textarea class="form-control" name="message" rows="4" required></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label>صورة العميل</label>
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="testimonialImage" accept="image/*">
                                            <label class="custom-file-label" for="testimonialImage">اختر صورة...</label>
                                        </div>
                                        <div id="testimonialImagePreview" class="mt-3" style="display: none;">
                                            <img id="testimonialPreviewImg" src="" alt="معاينة" style="max-width: 150px; max-height: 150px; border-radius: 50%;">
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="createTestimonial()" id="createTestimonialBtn">
                                    <span class="btn-text">إضافة الشهادة</span>
                                    <span class="btn-loading" style="display: none;">
                                        <i class="fas fa-spinner fa-spin"></i> جاري الحفظ...
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalContainer').innerHTML = modalHTML;
            $('#addTestimonialModal').modal('show');

            // Handle image preview
            document.getElementById('testimonialImage').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('testimonialPreviewImg').src = e.target.result;
                        document.getElementById('testimonialImagePreview').style.display = 'block';
                    };
                    reader.readAsDataURL(file);

                    // Update label
                    document.querySelector('.custom-file-label').textContent = file.name;
                }
            });
        }

        async function createTestimonial() {
            const form = document.getElementById('addTestimonialForm');
            const formData = new FormData(form);
            const testimonialData = Object.fromEntries(formData.entries());
            const btn = document.getElementById('createTestimonialBtn');

            try {
                // Show loading
                btn.querySelector('.btn-text').style.display = 'none';
                btn.querySelector('.btn-loading').style.display = 'inline';
                btn.disabled = true;

                // Upload image if selected
                const imageFile = document.getElementById('testimonialImage').files[0];
                if (imageFile) {
                    const uploadResult = await uploadImage(imageFile, 'testimonials');
                    testimonialData.image_url = uploadResult.fullUrl;
                }

                const response = await api.post('/testimonials', testimonialData);
                if (response.success) {
                    $('#addTestimonialModal').modal('hide');
                    dashboard.loadSection('testimonials');
                    showAlert('تم إضافة الشهادة بنجاح', 'success');
                } else {
                    showAlert(response.message || 'فشل في إضافة الشهادة', 'danger');
                }
            } catch (error) {
                showAlert('حدث خطأ أثناء إضافة الشهادة: ' + error.message, 'danger');
            } finally {
                // Hide loading
                btn.querySelector('.btn-text').style.display = 'inline';
                btn.querySelector('.btn-loading').style.display = 'none';
                btn.disabled = false;
            }
        }

        function editTestimonial(id) {
            // Get testimonial data first
            api.get(`/testimonials/${id}`).then(response => {
                if (response.success) {
                    const testimonial = response.data;
                    showEditTestimonialModal(testimonial);
                } else {
                    showAlert('فشل في جلب بيانات الشهادة', 'danger');
                }
            }).catch(error => {
                showAlert('حدث خطأ أثناء جلب بيانات الشهادة', 'danger');
            });
        }

        function showEditTestimonialModal(testimonial) {
            const modalHTML = `
                <div class="modal fade" id="editTestimonialModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">تعديل الشهادة</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <form id="editTestimonialForm">
                                    <input type="hidden" name="id" value="${testimonial.id}">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>اسم العميل *</label>
                                                <input type="text" class="form-control" name="name" value="${testimonial.name}" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>المنصب *</label>
                                                <input type="text" class="form-control" name="position" value="${testimonial.position}" required>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>الشركة</label>
                                                <input type="text" class="form-control" name="company" value="${testimonial.company || ''}">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>التقييم</label>
                                                <select class="form-control" name="rating">
                                                    <option value="5" ${testimonial.rating == 5 ? 'selected' : ''}>5 نجوم</option>
                                                    <option value="4" ${testimonial.rating == 4 ? 'selected' : ''}>4 نجوم</option>
                                                    <option value="3" ${testimonial.rating == 3 ? 'selected' : ''}>3 نجوم</option>
                                                    <option value="2" ${testimonial.rating == 2 ? 'selected' : ''}>2 نجوم</option>
                                                    <option value="1" ${testimonial.rating == 1 ? 'selected' : ''}>1 نجمة</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label>نص الشهادة *</label>
                                        <textarea class="form-control" name="message" rows="4" required>${testimonial.message}</textarea>
                                    </div>

                                    <div class="form-group">
                                        <label>صورة العميل الحالية</label>
                                        ${testimonial.image_url ? `
                                            <div class="current-image mb-3">
                                                <img src="${testimonial.image_url}" alt="الصورة الحالية" style="max-width: 150px; max-height: 150px; border-radius: 50%;">
                                                <div class="mt-2">
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeCurrentImage()">
                                                        <i class="fas fa-trash"></i> إزالة الصورة
                                                    </button>
                                                </div>
                                            </div>
                                        ` : '<p class="text-muted">لا توجد صورة حالية</p>'}

                                        <label>رفع صورة جديدة (اختياري)</label>
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="editTestimonialImage" accept="image/*">
                                            <label class="custom-file-label" for="editTestimonialImage">اختر صورة جديدة...</label>
                                        </div>
                                        <div id="editTestimonialImagePreview" class="mt-3" style="display: none;">
                                            <img id="editTestimonialPreviewImg" src="" alt="معاينة" style="max-width: 150px; max-height: 150px; border-radius: 50%;">
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="updateTestimonial()" id="updateTestimonialBtn">
                                    <span class="btn-text">حفظ التغييرات</span>
                                    <span class="btn-loading" style="display: none;">
                                        <i class="fas fa-spinner fa-spin"></i> جاري الحفظ...
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalContainer').innerHTML = modalHTML;
            $('#editTestimonialModal').modal('show');

            // Handle image preview
            document.getElementById('editTestimonialImage').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('editTestimonialPreviewImg').src = e.target.result;
                        document.getElementById('editTestimonialImagePreview').style.display = 'block';
                    };
                    reader.readAsDataURL(file);

                    // Update label
                    document.querySelector('label[for="editTestimonialImage"]').textContent = file.name;
                }
            });
        }

        function removeCurrentImage() {
            const currentImageDiv = document.querySelector('.current-image');
            if (currentImageDiv) {
                currentImageDiv.innerHTML = '<p class="text-success"><i class="fas fa-check"></i> سيتم إزالة الصورة عند الحفظ</p>';
                // Add hidden input to mark for removal
                const form = document.getElementById('editTestimonialForm');
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'remove_image';
                hiddenInput.value = 'true';
                form.appendChild(hiddenInput);
            }
        }

        async function updateTestimonial() {
            const form = document.getElementById('editTestimonialForm');
            const formData = new FormData(form);
            const testimonialData = Object.fromEntries(formData.entries());
            const btn = document.getElementById('updateTestimonialBtn');

            try {
                // Show loading
                btn.querySelector('.btn-text').style.display = 'none';
                btn.querySelector('.btn-loading').style.display = 'inline';
                btn.disabled = true;

                // Upload new image if selected
                const imageFile = document.getElementById('editTestimonialImage').files[0];
                if (imageFile) {
                    const uploadResult = await uploadImage(imageFile, 'testimonials');
                    testimonialData.image_url = uploadResult.fullUrl;
                } else if (testimonialData.remove_image === 'true') {
                    testimonialData.image_url = null;
                }

                // Remove form-specific fields
                delete testimonialData.remove_image;

                const response = await api.put(`/testimonials/${testimonialData.id}`, testimonialData);
                if (response.success) {
                    $('#editTestimonialModal').modal('hide');
                    dashboard.loadSection('testimonials');
                    showAlert('تم تحديث الشهادة بنجاح', 'success');
                } else {
                    showAlert(response.message || 'فشل في تحديث الشهادة', 'danger');
                }
            } catch (error) {
                showAlert('حدث خطأ أثناء تحديث الشهادة: ' + error.message, 'danger');
            } finally {
                // Hide loading
                btn.querySelector('.btn-text').style.display = 'inline';
                btn.querySelector('.btn-loading').style.display = 'none';
                btn.disabled = false;
            }
        }

        async function deleteTestimonial(id) {
            const confirmModal = `
                <div class="modal fade" id="confirmDeleteTestimonialModal" tabindex="-1">
                    <div class="modal-dialog modal-sm">
                        <div class="modal-content">
                            <div class="modal-header bg-danger text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-exclamation-triangle"></i> تأكيد الحذف
                                </h5>
                            </div>
                            <div class="modal-body text-center">
                                <i class="fas fa-comment-times fa-3x text-danger mb-3"></i>
                                <p><strong>هل أنت متأكد من حذف هذه الشهادة؟</strong></p>
                                <p class="text-muted">لا يمكن التراجع عن هذا الإجراء</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-danger" onclick="confirmDeleteTestimonial(${id})" id="confirmDeleteTestimonialBtn">
                                    <i class="fas fa-trash"></i> حذف نهائي
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalContainer').innerHTML = confirmModal;
            $('#confirmDeleteTestimonialModal').modal('show');
        }

        async function confirmDeleteTestimonial(id) {
            const btn = document.getElementById('confirmDeleteTestimonialBtn');

            try {
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحذف...';
                btn.disabled = true;

                const response = await api.delete(`/testimonials/${id}`);
                if (response.success) {
                    $('#confirmDeleteTestimonialModal').modal('hide');
                    dashboard.loadSection('testimonials');
                    showAlert('تم حذف الشهادة بنجاح', 'success');
                } else {
                    showAlert(response.message || 'فشل في حذف الشهادة', 'danger');
                }
            } catch (error) {
                showAlert('حدث خطأ أثناء حذف الشهادة', 'danger');
            } finally {
                btn.innerHTML = '<i class="fas fa-trash"></i> حذف نهائي';
                btn.disabled = false;
            }
        }

        // Gallery Functions
        function showAddGalleryModal() {
            const modalHTML = `
                <div class="modal fade" id="addGalleryModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">إضافة صورة جديدة للمعرض</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <form id="addGalleryForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>عنوان الصورة *</label>
                                                <input type="text" class="form-control" name="title" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>الفئة</label>
                                                <select class="form-control" name="category" id="galleryCategory">
                                                    <option value="general">عام</option>
                                                    <option value="nature">طبيعة</option>
                                                    <option value="city">مدينة</option>
                                                    <option value="portrait">بورتريه</option>
                                                    <option value="business">أعمال</option>
                                                    <option value="health">صحة</option>
                                                </select>
                                                <small class="form-text text-muted">أو اكتب فئة جديدة:</small>
                                                <input type="text" class="form-control mt-1" id="customCategory" placeholder="فئة جديدة...">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label>وصف الصورة</label>
                                        <textarea class="form-control" name="description" rows="3"></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label>ترتيب العرض</label>
                                        <input type="number" class="form-control" name="sort_order" value="0" min="0">
                                    </div>

                                    <div class="form-group">
                                        <label>الصورة الرئيسية *</label>
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="galleryMainImage" accept="image/*" required>
                                            <label class="custom-file-label" for="galleryMainImage">اختر الصورة الرئيسية...</label>
                                        </div>
                                        <div id="galleryMainImagePreview" class="mt-3" style="display: none;">
                                            <img id="galleryMainPreviewImg" src="" alt="معاينة" style="max-width: 100%; max-height: 300px;">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label>صورة مصغرة (اختيارية)</label>
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="galleryThumbnailImage" accept="image/*">
                                            <label class="custom-file-label" for="galleryThumbnailImage">اختر الصورة المصغرة...</label>
                                        </div>
                                        <small class="form-text text-muted">إذا لم تختر صورة مصغرة، سيتم استخدام الصورة الرئيسية</small>
                                        <div id="galleryThumbnailImagePreview" class="mt-3" style="display: none;">
                                            <img id="galleryThumbnailPreviewImg" src="" alt="معاينة مصغرة" style="max-width: 150px; max-height: 150px;">
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="createGalleryItem()" id="createGalleryBtn">
                                    <span class="btn-text">إضافة للمعرض</span>
                                    <span class="btn-loading" style="display: none;">
                                        <i class="fas fa-spinner fa-spin"></i> جاري الرفع...
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalContainer').innerHTML = modalHTML;
            $('#addGalleryModal').modal('show');

            // Handle custom category
            document.getElementById('customCategory').addEventListener('input', function(e) {
                if (e.target.value) {
                    document.getElementById('galleryCategory').value = e.target.value;
                }
            });

            // Handle main image preview
            document.getElementById('galleryMainImage').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('galleryMainPreviewImg').src = e.target.result;
                        document.getElementById('galleryMainImagePreview').style.display = 'block';
                    };
                    reader.readAsDataURL(file);

                    // Update label
                    document.querySelector('label[for="galleryMainImage"]').textContent = file.name;
                }
            });

            // Handle thumbnail image preview
            document.getElementById('galleryThumbnailImage').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('galleryThumbnailPreviewImg').src = e.target.result;
                        document.getElementById('galleryThumbnailImagePreview').style.display = 'block';
                    };
                    reader.readAsDataURL(file);

                    // Update label
                    document.querySelector('label[for="galleryThumbnailImage"]').textContent = file.name;
                }
            });
        }

        async function createGalleryItem() {
            const form = document.getElementById('addGalleryForm');
            const formData = new FormData(form);
            const galleryData = Object.fromEntries(formData.entries());
            const btn = document.getElementById('createGalleryBtn');

            try {
                // Show loading
                btn.querySelector('.btn-text').style.display = 'none';
                btn.querySelector('.btn-loading').style.display = 'inline';
                btn.disabled = true;

                // Upload main image
                const mainImageFile = document.getElementById('galleryMainImage').files[0];
                if (!mainImageFile) {
                    throw new Error('يرجى اختيار الصورة الرئيسية');
                }

                const mainImageResult = await uploadImage(mainImageFile, 'gallery');
                galleryData.image_url = mainImageResult.fullUrl;

                // Upload thumbnail if provided
                const thumbnailImageFile = document.getElementById('galleryThumbnailImage').files[0];
                if (thumbnailImageFile) {
                    const thumbnailResult = await uploadImage(thumbnailImageFile, 'gallery');
                    galleryData.thumbnail_url = thumbnailResult.fullUrl;
                } else {
                    galleryData.thumbnail_url = mainImageResult.fullUrl;
                }

                // Use custom category if provided
                const customCategory = document.getElementById('customCategory').value;
                if (customCategory) {
                    galleryData.category = customCategory;
                }

                const response = await api.post('/gallery', galleryData);
                if (response.success) {
                    $('#addGalleryModal').modal('hide');
                    dashboard.loadSection('gallery');
                    showAlert('تم إضافة الصورة للمعرض بنجاح', 'success');
                } else {
                    showAlert(response.message || 'فشل في إضافة الصورة', 'danger');
                }
            } catch (error) {
                showAlert('حدث خطأ أثناء إضافة الصورة: ' + error.message, 'danger');
            } finally {
                // Hide loading
                btn.querySelector('.btn-text').style.display = 'inline';
                btn.querySelector('.btn-loading').style.display = 'none';
                btn.disabled = false;
            }
        }

        function editGalleryItem(id) {
            // Get gallery item data first
            api.get(`/gallery/${id}`).then(response => {
                if (response.success) {
                    const item = response.data;
                    showEditGalleryModal(item);
                } else {
                    showAlert('فشل في جلب بيانات الصورة', 'danger');
                }
            }).catch(error => {
                showAlert('حدث خطأ أثناء جلب بيانات الصورة', 'danger');
            });
        }

        function showEditGalleryModal(item) {
            const modalHTML = `
                <div class="modal fade" id="editGalleryModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">تعديل صورة المعرض</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <form id="editGalleryForm">
                                    <input type="hidden" name="id" value="${item.id}">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>عنوان الصورة *</label>
                                                <input type="text" class="form-control" name="title" value="${item.title}" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>الفئة</label>
                                                <select class="form-control" name="category" id="editGalleryCategory">
                                                    <option value="general" ${item.category === 'general' ? 'selected' : ''}>عام</option>
                                                    <option value="nature" ${item.category === 'nature' ? 'selected' : ''}>طبيعة</option>
                                                    <option value="city" ${item.category === 'city' ? 'selected' : ''}>مدينة</option>
                                                    <option value="portrait" ${item.category === 'portrait' ? 'selected' : ''}>بورتريه</option>
                                                    <option value="business" ${item.category === 'business' ? 'selected' : ''}>أعمال</option>
                                                    <option value="health" ${item.category === 'health' ? 'selected' : ''}>صحة</option>
                                                </select>
                                                <small class="form-text text-muted">أو اكتب فئة جديدة:</small>
                                                <input type="text" class="form-control mt-1" id="editCustomCategory" placeholder="فئة جديدة..." value="${!['general', 'nature', 'city', 'portrait', 'business', 'health'].includes(item.category) ? item.category : ''}">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label>وصف الصورة</label>
                                        <textarea class="form-control" name="description" rows="3">${item.description || ''}</textarea>
                                    </div>

                                    <div class="form-group">
                                        <label>ترتيب العرض</label>
                                        <input type="number" class="form-control" name="sort_order" value="${item.sort_order || 0}" min="0">
                                    </div>

                                    <div class="form-group">
                                        <label>الصورة الحالية</label>
                                        <div class="current-images mb-3">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label class="small">الصورة الرئيسية:</label>
                                                    <img src="${item.image_url}" alt="الصورة الرئيسية" style="max-width: 100%; max-height: 200px;" class="d-block mb-2">
                                                </div>
                                                ${item.thumbnail_url && item.thumbnail_url !== item.image_url ? `
                                                    <div class="col-md-6">
                                                        <label class="small">الصورة المصغرة:</label>
                                                        <img src="${item.thumbnail_url}" alt="الصورة المصغرة" style="max-width: 100%; max-height: 200px;" class="d-block mb-2">
                                                    </div>
                                                ` : ''}
                                            </div>
                                        </div>

                                        <label>رفع صورة رئيسية جديدة (اختياري)</label>
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="editGalleryMainImage" accept="image/*">
                                            <label class="custom-file-label" for="editGalleryMainImage">اختر صورة رئيسية جديدة...</label>
                                        </div>
                                        <div id="editGalleryMainImagePreview" class="mt-3" style="display: none;">
                                            <img id="editGalleryMainPreviewImg" src="" alt="معاينة" style="max-width: 100%; max-height: 300px;">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label>رفع صورة مصغرة جديدة (اختياري)</label>
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="editGalleryThumbnailImage" accept="image/*">
                                            <label class="custom-file-label" for="editGalleryThumbnailImage">اختر صورة مصغرة جديدة...</label>
                                        </div>
                                        <small class="form-text text-muted">إذا لم تختر صورة مصغرة، سيتم استخدام الصورة الرئيسية</small>
                                        <div id="editGalleryThumbnailImagePreview" class="mt-3" style="display: none;">
                                            <img id="editGalleryThumbnailPreviewImg" src="" alt="معاينة مصغرة" style="max-width: 150px; max-height: 150px;">
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="updateGalleryItem()" id="updateGalleryBtn">
                                    <span class="btn-text">حفظ التغييرات</span>
                                    <span class="btn-loading" style="display: none;">
                                        <i class="fas fa-spinner fa-spin"></i> جاري الحفظ...
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalContainer').innerHTML = modalHTML;
            $('#editGalleryModal').modal('show');

            // Handle custom category
            document.getElementById('editCustomCategory').addEventListener('input', function(e) {
                if (e.target.value) {
                    document.getElementById('editGalleryCategory').value = e.target.value;
                }
            });

            // Handle main image preview
            document.getElementById('editGalleryMainImage').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('editGalleryMainPreviewImg').src = e.target.result;
                        document.getElementById('editGalleryMainImagePreview').style.display = 'block';
                    };
                    reader.readAsDataURL(file);

                    // Update label
                    document.querySelector('label[for="editGalleryMainImage"]').textContent = file.name;
                }
            });

            // Handle thumbnail image preview
            document.getElementById('editGalleryThumbnailImage').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('editGalleryThumbnailPreviewImg').src = e.target.result;
                        document.getElementById('editGalleryThumbnailImagePreview').style.display = 'block';
                    };
                    reader.readAsDataURL(file);

                    // Update label
                    document.querySelector('label[for="editGalleryThumbnailImage"]').textContent = file.name;
                }
            });
        }

        async function updateGalleryItem() {
            const form = document.getElementById('editGalleryForm');
            const formData = new FormData(form);
            const galleryData = Object.fromEntries(formData.entries());
            const btn = document.getElementById('updateGalleryBtn');

            try {
                // Show loading
                btn.querySelector('.btn-text').style.display = 'none';
                btn.querySelector('.btn-loading').style.display = 'inline';
                btn.disabled = true;

                // Upload new main image if selected
                const mainImageFile = document.getElementById('editGalleryMainImage').files[0];
                if (mainImageFile) {
                    const mainImageResult = await uploadImage(mainImageFile, 'gallery');
                    galleryData.image_url = mainImageResult.fullUrl;
                }

                // Upload new thumbnail if provided
                const thumbnailImageFile = document.getElementById('editGalleryThumbnailImage').files[0];
                if (thumbnailImageFile) {
                    const thumbnailResult = await uploadImage(thumbnailImageFile, 'gallery');
                    galleryData.thumbnail_url = thumbnailResult.fullUrl;
                } else if (mainImageFile) {
                    // If main image changed but no thumbnail, use main image as thumbnail
                    galleryData.thumbnail_url = galleryData.image_url;
                }

                // Use custom category if provided
                const customCategory = document.getElementById('editCustomCategory').value;
                if (customCategory) {
                    galleryData.category = customCategory;
                }

                const response = await api.put(`/gallery/${galleryData.id}`, galleryData);
                if (response.success) {
                    $('#editGalleryModal').modal('hide');
                    dashboard.loadSection('gallery');
                    showAlert('تم تحديث الصورة بنجاح', 'success');
                } else {
                    showAlert(response.message || 'فشل في تحديث الصورة', 'danger');
                }
            } catch (error) {
                showAlert('حدث خطأ أثناء تحديث الصورة: ' + error.message, 'danger');
            } finally {
                // Hide loading
                btn.querySelector('.btn-text').style.display = 'inline';
                btn.querySelector('.btn-loading').style.display = 'none';
                btn.disabled = false;
            }
        }

        async function deleteGalleryItem(id) {
            const confirmModal = `
                <div class="modal fade" id="confirmDeleteGalleryModal" tabindex="-1">
                    <div class="modal-dialog modal-sm">
                        <div class="modal-content">
                            <div class="modal-header bg-danger text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-exclamation-triangle"></i> تأكيد الحذف
                                </h5>
                            </div>
                            <div class="modal-body text-center">
                                <i class="fas fa-image fa-3x text-danger mb-3"></i>
                                <p><strong>هل أنت متأكد من حذف هذه الصورة؟</strong></p>
                                <p class="text-muted">لا يمكن التراجع عن هذا الإجراء</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-danger" onclick="confirmDeleteGalleryItem(${id})" id="confirmDeleteGalleryBtn">
                                    <i class="fas fa-trash"></i> حذف نهائي
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalContainer').innerHTML = confirmModal;
            $('#confirmDeleteGalleryModal').modal('show');
        }

        async function confirmDeleteGalleryItem(id) {
            const btn = document.getElementById('confirmDeleteGalleryBtn');

            try {
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحذف...';
                btn.disabled = true;

                const response = await api.delete(`/gallery/${id}`);
                if (response.success) {
                    $('#confirmDeleteGalleryModal').modal('hide');
                    dashboard.loadSection('gallery');
                    showAlert('تم حذف الصورة بنجاح', 'success');
                } else {
                    showAlert(response.message || 'فشل في حذف الصورة', 'danger');
                }
            } catch (error) {
                showAlert('حدث خطأ أثناء حذف الصورة', 'danger');
            } finally {
                btn.innerHTML = '<i class="fas fa-trash"></i> حذف نهائي';
                btn.disabled = false;
            }
        }

        function filterGallery(category) {
            const items = document.querySelectorAll('.gallery-item');
            const buttons = document.querySelectorAll('.btn-group button');

            // Update active button
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Filter items
            items.forEach(item => {
                if (category === 'all' || item.dataset.category === category) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function viewGalleryItem(imageUrl) {
            const modalHTML = `
                <div class="modal fade" id="viewImageModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">عرض الصورة</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body text-center">
                                <img src="${imageUrl}" class="img-fluid" alt="صورة">
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalContainer').innerHTML = modalHTML;
            $('#viewImageModal').modal('show');
        }

        // Message Functions
        async function viewMessage(id) {
            try {
                const response = await api.get(`/contact/${id}`);
                if (response.success) {
                    const message = response.data;
                    const modalHTML = `
                        <div class="modal fade" id="viewMessageModal" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">عرض الرسالة</h5>
                                        <button type="button" class="close" data-dismiss="modal">
                                            <span>&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <strong>الاسم:</strong> ${message.name}
                                            </div>
                                            <div class="col-md-6">
                                                <strong>البريد الإلكتروني:</strong> ${message.email}
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-6">
                                                <strong>الموضوع:</strong> ${message.subject || 'بدون موضوع'}
                                            </div>
                                            <div class="col-md-6">
                                                <strong>التاريخ:</strong> ${new Date(message.created_at).toLocaleString('ar-SA')}
                                            </div>
                                        </div>
                                        <hr>
                                        <div>
                                            <strong>الرسالة:</strong>
                                            <div class="mt-2 p-3 bg-light rounded">
                                                ${message.message}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-success" onclick="markAsRead(${id})">
                                            تم القراءة
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="replyMessage(${id})">
                                            رد
                                        </button>
                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                            إغلاق
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    document.getElementById('modalContainer').innerHTML = modalHTML;
                    $('#viewMessageModal').modal('show');
                }
            } catch (error) {
                showAlert('حدث خطأ أثناء عرض الرسالة', 'danger');
            }
        }

        async function markAsRead(id) {
            try {
                await api.put(`/contact/${id}/status`, { status: 'read' });
                $('#viewMessageModal').modal('hide');
                dashboard.loadSection('messages');
                showAlert('تم تحديث حالة الرسالة', 'success');
            } catch (error) {
                showAlert('حدث خطأ أثناء تحديث الرسالة', 'danger');
            }
        }

        function replyMessage(id) {
            alert('ميزة الرد على الرسائل قيد التطوير');
        }

        async function deleteMessage(id) {
            if (confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
                try {
                    const response = await api.delete(`/contact/${id}`);
                    if (response.success) {
                        dashboard.loadSection('messages');
                        showAlert('تم حذف الرسالة بنجاح', 'success');
                    }
                } catch (error) {
                    showAlert('حدث خطأ أثناء حذف الرسالة', 'danger');
                }
            }
        }

        // System Functions
        function backupDatabase() {
            alert('ميزة النسخ الاحتياطي قيد التطوير');
        }

        function clearCache() {
            if (confirm('هل أنت متأكد من مسح الذاكرة المؤقتة؟')) {
                alert('تم مسح الذاكرة المؤقتة');
            }
        }

        function checkUpdates() {
            alert('لا توجد تحديثات متاحة حالياً');
        }

        // Service Functions
        function showAddServiceModal() {
            const modalHTML = `
                <div class="modal fade" id="addServiceModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">إضافة خدمة جديدة</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <form id="addServiceForm">
                                    <div class="form-group">
                                        <label>عنوان الخدمة *</label>
                                        <input type="text" class="form-control" name="title" required>
                                    </div>
                                    <div class="form-group">
                                        <label>وصف الخدمة *</label>
                                        <textarea class="form-control" name="description" rows="4" required></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label>أيقونة الخدمة *</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">
                                                    <i id="iconPreview" class="fas fa-cog"></i>
                                                </span>
                                            </div>
                                            <input type="text" class="form-control" name="icon" value="fas fa-cog" required>
                                        </div>
                                        <small class="form-text text-muted">
                                            استخدم أيقونات Font Awesome مثل: fas fa-laptop, fas fa-mobile-alt, fas fa-chart-line
                                        </small>
                                    </div>
                                    <div class="form-group">
                                        <label>ترتيب العرض</label>
                                        <input type="number" class="form-control" name="sort_order" value="0" min="0">
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="createService()">إضافة الخدمة</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalContainer').innerHTML = modalHTML;
            $('#addServiceModal').modal('show');

            // Handle icon preview
            document.querySelector('input[name="icon"]').addEventListener('input', function(e) {
                const iconPreview = document.getElementById('iconPreview');
                iconPreview.className = e.target.value || 'fas fa-cog';
            });
        }

        async function createService() {
            const form = document.getElementById('addServiceForm');
            const formData = new FormData(form);
            const serviceData = Object.fromEntries(formData.entries());

            try {
                const response = await api.post('/services', serviceData);
                if (response.success) {
                    $('#addServiceModal').modal('hide');
                    dashboard.loadSection('services');
                    showAlert('تم إضافة الخدمة بنجاح', 'success');
                } else {
                    showAlert(response.message || 'فشل في إضافة الخدمة', 'danger');
                }
            } catch (error) {
                showAlert('حدث خطأ أثناء إضافة الخدمة', 'danger');
            }
        }

        function editService(id) {
            // Get service data first
            api.get(`/services/${id}`).then(response => {
                if (response.success) {
                    const service = response.data;
                    showEditServiceModal(service);
                } else {
                    showAlert('فشل في جلب بيانات الخدمة', 'danger');
                }
            }).catch(error => {
                showAlert('حدث خطأ أثناء جلب بيانات الخدمة', 'danger');
            });
        }

        function showEditServiceModal(service) {
            const modalHTML = `
                <div class="modal fade" id="editServiceModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">تعديل الخدمة</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <form id="editServiceForm">
                                    <input type="hidden" name="id" value="${service.id}">
                                    <div class="form-group">
                                        <label>عنوان الخدمة *</label>
                                        <input type="text" class="form-control" name="title" value="${service.title}" required>
                                    </div>
                                    <div class="form-group">
                                        <label>وصف الخدمة *</label>
                                        <textarea class="form-control" name="description" rows="4" required>${service.description}</textarea>
                                    </div>
                                    <div class="form-group">
                                        <label>أيقونة الخدمة *</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">
                                                    <i id="editIconPreview" class="${service.icon}"></i>
                                                </span>
                                            </div>
                                            <input type="text" class="form-control" name="icon" value="${service.icon}" required>
                                        </div>
                                        <small class="form-text text-muted">
                                            استخدم أيقونات Font Awesome مثل: fas fa-laptop, fas fa-mobile-alt, fas fa-chart-line
                                        </small>
                                    </div>
                                    <div class="form-group">
                                        <label>ترتيب العرض</label>
                                        <input type="number" class="form-control" name="sort_order" value="${service.sort_order || 0}" min="0">
                                    </div>
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" name="is_active" ${service.is_active ? 'checked' : ''}>
                                            <label class="form-check-label">خدمة نشطة</label>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="updateService()" id="updateServiceBtn">
                                    <span class="btn-text">حفظ التغييرات</span>
                                    <span class="btn-loading" style="display: none;">
                                        <i class="fas fa-spinner fa-spin"></i> جاري الحفظ...
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalContainer').innerHTML = modalHTML;
            $('#editServiceModal').modal('show');

            // Handle icon preview
            document.querySelector('input[name="icon"]').addEventListener('input', function(e) {
                const iconPreview = document.getElementById('editIconPreview');
                iconPreview.className = e.target.value || 'fas fa-cog';
            });
        }

        async function updateService() {
            const form = document.getElementById('editServiceForm');
            const formData = new FormData(form);
            const serviceData = Object.fromEntries(formData.entries());
            const btn = document.getElementById('updateServiceBtn');

            // Convert checkbox to boolean
            serviceData.is_active = formData.has('is_active');

            try {
                // Show loading
                btn.querySelector('.btn-text').style.display = 'none';
                btn.querySelector('.btn-loading').style.display = 'inline';
                btn.disabled = true;

                const response = await api.put(`/services/${serviceData.id}`, serviceData);
                if (response.success) {
                    $('#editServiceModal').modal('hide');
                    dashboard.loadSection('services');
                    showAlert('تم تحديث الخدمة بنجاح', 'success');
                } else {
                    showAlert(response.message || 'فشل في تحديث الخدمة', 'danger');
                }
            } catch (error) {
                showAlert('حدث خطأ أثناء تحديث الخدمة', 'danger');
            } finally {
                // Hide loading
                btn.querySelector('.btn-text').style.display = 'inline';
                btn.querySelector('.btn-loading').style.display = 'none';
                btn.disabled = false;
            }
        }

        async function deleteService(id) {
            const confirmModal = `
                <div class="modal fade" id="confirmDeleteServiceModal" tabindex="-1">
                    <div class="modal-dialog modal-sm">
                        <div class="modal-content">
                            <div class="modal-header bg-danger text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-exclamation-triangle"></i> تأكيد الحذف
                                </h5>
                            </div>
                            <div class="modal-body text-center">
                                <i class="fas fa-cogs fa-3x text-danger mb-3"></i>
                                <p><strong>هل أنت متأكد من حذف هذه الخدمة؟</strong></p>
                                <p class="text-muted">لا يمكن التراجع عن هذا الإجراء</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-danger" onclick="confirmDeleteService(${id})" id="confirmDeleteServiceBtn">
                                    <i class="fas fa-trash"></i> حذف نهائي
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalContainer').innerHTML = confirmModal;
            $('#confirmDeleteServiceModal').modal('show');
        }

        async function confirmDeleteService(id) {
            const btn = document.getElementById('confirmDeleteServiceBtn');

            try {
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحذف...';
                btn.disabled = true;

                const response = await api.delete(`/services/${id}`);
                if (response.success) {
                    $('#confirmDeleteServiceModal').modal('hide');
                    dashboard.loadSection('services');
                    showAlert('تم حذف الخدمة بنجاح', 'success');
                } else {
                    showAlert(response.message || 'فشل في حذف الخدمة', 'danger');
                }
            } catch (error) {
                showAlert('حدث خطأ أثناء حذف الخدمة', 'danger');
            } finally {
                btn.innerHTML = '<i class="fas fa-trash"></i> حذف نهائي';
                btn.disabled = false;
            }
        }

        async function toggleService(id) {
            try {
                const response = await api.put(`/services/${id}/toggle-status`, {});
                if (response.success) {
                    dashboard.loadSection('services');
                    showAlert('تم تحديث حالة الخدمة', 'success');
                } else {
                    showAlert(response.message || 'فشل في تحديث حالة الخدمة', 'danger');
                }
            } catch (error) {
                showAlert('حدث خطأ أثناء تحديث الخدمة', 'danger');
            }
        }

        // Settings Functions
        function saveHomeSettings() {
            showAlert('تم حفظ إعدادات الصفحة الرئيسية', 'success');
        }

        function saveContactSettings() {
            showAlert('تم حفظ إعدادات التواصل', 'success');
        }

        function saveDesignSettings() {
            showAlert('تم حفظ إعدادات التصميم', 'success');
        }

        // Utility Functions
        function showAlert(message, type) {
            const alertHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            `;

            const container = document.getElementById('alertContainer');
            container.innerHTML = alertHTML;

            // Auto dismiss after 5 seconds
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    $(alert).alert('close');
                }
            }, 5000);
        }

        // Initialize dashboard
        let dashboard;
        document.addEventListener('DOMContentLoaded', () => {
            dashboard = new DashboardManager();
        });
    </script>
</body>
</html>
