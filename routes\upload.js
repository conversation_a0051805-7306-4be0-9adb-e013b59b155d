const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { authenticate, authorize } = require('../middleware/auth');
const router = express.Router();

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
}

// Create subdirectories
const subdirs = ['gallery', 'testimonials', 'avatars', 'temp'];
subdirs.forEach(dir => {
    const dirPath = path.join(uploadsDir, dir);
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
    }
});

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadType = req.body.type || 'temp';
        const uploadPath = path.join(uploadsDir, uploadType);
        cb(null, uploadPath);
    },
    filename: function (req, file, cb) {
        // Generate unique filename
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        cb(null, file.fieldname + '-' + uniqueSuffix + ext);
    }
});

// File filter for images
const fileFilter = (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('نوع الملف غير مدعوم. يرجى رفع صور بصيغة JPG, PNG, GIF, أو WebP فقط.'), false);
    }
};

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    },
    fileFilter: fileFilter
});

// POST /api/upload/image - Upload single image
router.post('/image', authenticate, upload.single('image'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: 'لم يتم رفع أي ملف'
            });
        }

        const fileUrl = `/uploads/${req.body.type || 'temp'}/${req.file.filename}`;
        
        res.json({
            success: true,
            message: 'تم رفع الصورة بنجاح',
            data: {
                filename: req.file.filename,
                originalName: req.file.originalname,
                size: req.file.size,
                url: fileUrl,
                fullUrl: `${req.protocol}://${req.get('host')}${fileUrl}`
            }
        });
    } catch (error) {
        console.error('Upload error:', error);
        res.status(500).json({
            success: false,
            message: 'فشل في رفع الصورة'
        });
    }
});

// POST /api/upload/multiple - Upload multiple images
router.post('/multiple', authenticate, upload.array('images', 10), async (req, res) => {
    try {
        if (!req.files || req.files.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'لم يتم رفع أي ملفات'
            });
        }

        const uploadedFiles = req.files.map(file => {
            const fileUrl = `/uploads/${req.body.type || 'temp'}/${file.filename}`;
            return {
                filename: file.filename,
                originalName: file.originalname,
                size: file.size,
                url: fileUrl,
                fullUrl: `${req.protocol}://${req.get('host')}${fileUrl}`
            };
        });

        res.json({
            success: true,
            message: `تم رفع ${uploadedFiles.length} صورة بنجاح`,
            data: uploadedFiles
        });
    } catch (error) {
        console.error('Multiple upload error:', error);
        res.status(500).json({
            success: false,
            message: 'فشل في رفع الصور'
        });
    }
});

// DELETE /api/upload/:filename - Delete uploaded file
router.delete('/:type/:filename', authenticate, authorize('admin'), async (req, res) => {
    try {
        const { type, filename } = req.params;
        const filePath = path.join(uploadsDir, type, filename);

        if (!fs.existsSync(filePath)) {
            return res.status(404).json({
                success: false,
                message: 'الملف غير موجود'
            });
        }

        fs.unlinkSync(filePath);

        res.json({
            success: true,
            message: 'تم حذف الملف بنجاح'
        });
    } catch (error) {
        console.error('Delete file error:', error);
        res.status(500).json({
            success: false,
            message: 'فشل في حذف الملف'
        });
    }
});

// GET /api/upload/list/:type - List files in directory
router.get('/list/:type', authenticate, async (req, res) => {
    try {
        const { type } = req.params;
        const dirPath = path.join(uploadsDir, type);

        if (!fs.existsSync(dirPath)) {
            return res.json({
                success: true,
                data: []
            });
        }

        const files = fs.readdirSync(dirPath).map(filename => {
            const filePath = path.join(dirPath, filename);
            const stats = fs.statSync(filePath);
            const fileUrl = `/uploads/${type}/${filename}`;

            return {
                filename,
                size: stats.size,
                created: stats.birthtime,
                modified: stats.mtime,
                url: fileUrl,
                fullUrl: `${req.protocol}://${req.get('host')}${fileUrl}`
            };
        });

        res.json({
            success: true,
            data: files
        });
    } catch (error) {
        console.error('List files error:', error);
        res.status(500).json({
            success: false,
            message: 'فشل في جلب قائمة الملفات'
        });
    }
});

// POST /api/upload/base64 - Upload base64 image
router.post('/base64', authenticate, async (req, res) => {
    try {
        const { imageData, filename, type = 'temp' } = req.body;

        if (!imageData) {
            return res.status(400).json({
                success: false,
                message: 'بيانات الصورة مطلوبة'
            });
        }

        // Extract base64 data
        const matches = imageData.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/);
        if (!matches) {
            return res.status(400).json({
                success: false,
                message: 'تنسيق الصورة غير صحيح'
            });
        }

        const imageType = matches[1];
        const base64Data = matches[2];
        const buffer = Buffer.from(base64Data, 'base64');

        // Generate filename
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const finalFilename = filename || `image-${uniqueSuffix}.${imageType}`;
        
        const uploadPath = path.join(uploadsDir, type);
        const filePath = path.join(uploadPath, finalFilename);

        // Save file
        fs.writeFileSync(filePath, buffer);

        const fileUrl = `/uploads/${type}/${finalFilename}`;

        res.json({
            success: true,
            message: 'تم رفع الصورة بنجاح',
            data: {
                filename: finalFilename,
                size: buffer.length,
                url: fileUrl,
                fullUrl: `${req.protocol}://${req.get('host')}${fileUrl}`
            }
        });
    } catch (error) {
        console.error('Base64 upload error:', error);
        res.status(500).json({
            success: false,
            message: 'فشل في رفع الصورة'
        });
    }
});

// Error handling middleware
router.use((error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                success: false,
                message: 'حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت'
            });
        }
        if (error.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
                success: false,
                message: 'عدد الملفات كبير جداً. الحد الأقصى 10 ملفات'
            });
        }
    }

    res.status(400).json({
        success: false,
        message: error.message || 'خطأ في رفع الملف'
    });
});

module.exports = router;
