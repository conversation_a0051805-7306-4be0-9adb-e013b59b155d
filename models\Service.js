const db = require('../config/database');

class Service {
    // Get all active services
    static async getAll() {
        try {
            const services = await db.query(
                'SELECT * FROM services WHERE is_active = 1 ORDER BY sort_order ASC, created_at DESC'
            );
            return services;
        } catch (error) {
            throw new Error('Error fetching services: ' + error.message);
        }
    }

    // Get service by ID
    static async getById(id) {
        try {
            const service = await db.get(
                'SELECT * FROM services WHERE id = ?',
                [id]
            );
            return service;
        } catch (error) {
            throw new Error('Error fetching service: ' + error.message);
        }
    }

    // Create new service
    static async create(data) {
        try {
            const { title, description, icon, sort_order } = data;
            const result = await db.run(
                `INSERT INTO services (title, description, icon, sort_order) 
                 VALUES (?, ?, ?, ?)`,
                [title, description, icon, sort_order || 0]
            );
            return result.id;
        } catch (error) {
            throw new Error('Error creating service: ' + error.message);
        }
    }

    // Update service
    static async update(id, data) {
        try {
            const { title, description, icon, sort_order, is_active } = data;
            const result = await db.run(
                `UPDATE services 
                 SET title = ?, description = ?, icon = ?, sort_order = ?, 
                     is_active = ?, updated_at = CURRENT_TIMESTAMP
                 WHERE id = ?`,
                [title, description, icon, sort_order, is_active, id]
            );
            return result.changes > 0;
        } catch (error) {
            throw new Error('Error updating service: ' + error.message);
        }
    }

    // Delete service (soft delete)
    static async delete(id) {
        try {
            const result = await db.run(
                'UPDATE services SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [id]
            );
            return result.changes > 0;
        } catch (error) {
            throw new Error('Error deleting service: ' + error.message);
        }
    }

    // Update sort order
    static async updateSortOrder(id, sortOrder) {
        try {
            const result = await db.run(
                'UPDATE services SET sort_order = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [sortOrder, id]
            );
            return result.changes > 0;
        } catch (error) {
            throw new Error('Error updating sort order: ' + error.message);
        }
    }

    // Get services with pagination
    static async getPaginated(page = 1, limit = 10) {
        try {
            const offset = (page - 1) * limit;
            const services = await db.query(
                'SELECT * FROM services WHERE is_active = 1 ORDER BY sort_order ASC LIMIT ? OFFSET ?',
                [limit, offset]
            );
            
            const totalResult = await db.get(
                'SELECT COUNT(*) as total FROM services WHERE is_active = 1'
            );
            
            return {
                services,
                total: totalResult.total,
                page,
                limit,
                totalPages: Math.ceil(totalResult.total / limit)
            };
        } catch (error) {
            throw new Error('Error fetching paginated services: ' + error.message);
        }
    }

    // Toggle service status
    static async toggleStatus(id) {
        try {
            const service = await this.getById(id);
            if (!service) {
                throw new Error('Service not found');
            }

            const newStatus = service.is_active ? 0 : 1;
            const result = await db.run(
                'UPDATE services SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [newStatus, id]
            );
            return result.changes > 0;
        } catch (error) {
            throw new Error('Error toggling service status: ' + error.message);
        }
    }

    // Reorder services
    static async reorder(serviceOrders) {
        try {
            const promises = serviceOrders.map(({ id, sort_order }) => 
                db.run(
                    'UPDATE services SET sort_order = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [sort_order, id]
                )
            );
            
            await Promise.all(promises);
            return true;
        } catch (error) {
            throw new Error('Error reordering services: ' + error.message);
        }
    }
}

module.exports = Service;
