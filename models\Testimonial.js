const db = require('../config/database');

class Testimonial {
    // Get all active testimonials
    static async getAll() {
        try {
            const testimonials = await db.query(
                'SELECT * FROM testimonials WHERE is_active = 1 ORDER BY created_at DESC'
            );
            return testimonials;
        } catch (error) {
            throw new Error('Error fetching testimonials: ' + error.message);
        }
    }

    // Get testimonial by ID
    static async getById(id) {
        try {
            const testimonial = await db.get(
                'SELECT * FROM testimonials WHERE id = ?',
                [id]
            );
            return testimonial;
        } catch (error) {
            throw new Error('Error fetching testimonial: ' + error.message);
        }
    }

    // Create new testimonial
    static async create(data) {
        try {
            const { name, position, company, message, image_url, rating } = data;
            const result = await db.run(
                `INSERT INTO testimonials (name, position, company, message, image_url, rating) 
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [name, position, company || null, message, image_url || null, rating || 5]
            );
            return result.id;
        } catch (error) {
            throw new Error('Error creating testimonial: ' + error.message);
        }
    }

    // Update testimonial
    static async update(id, data) {
        try {
            const { name, position, company, message, image_url, rating, is_active } = data;
            const result = await db.run(
                `UPDATE testimonials 
                 SET name = ?, position = ?, company = ?, message = ?, 
                     image_url = ?, rating = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                 WHERE id = ?`,
                [name, position, company, message, image_url, rating, is_active, id]
            );
            return result.changes > 0;
        } catch (error) {
            throw new Error('Error updating testimonial: ' + error.message);
        }
    }

    // Delete testimonial (soft delete)
    static async delete(id) {
        try {
            const result = await db.run(
                'UPDATE testimonials SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [id]
            );
            return result.changes > 0;
        } catch (error) {
            throw new Error('Error deleting testimonial: ' + error.message);
        }
    }

    // Get testimonials with pagination
    static async getPaginated(page = 1, limit = 10) {
        try {
            const offset = (page - 1) * limit;
            const testimonials = await db.query(
                'SELECT * FROM testimonials WHERE is_active = 1 ORDER BY created_at DESC LIMIT ? OFFSET ?',
                [limit, offset]
            );
            
            const totalResult = await db.get(
                'SELECT COUNT(*) as total FROM testimonials WHERE is_active = 1'
            );
            
            return {
                testimonials,
                total: totalResult.total,
                page,
                limit,
                totalPages: Math.ceil(totalResult.total / limit)
            };
        } catch (error) {
            throw new Error('Error fetching paginated testimonials: ' + error.message);
        }
    }

    // Get testimonials by rating
    static async getByRating(rating) {
        try {
            const testimonials = await db.query(
                'SELECT * FROM testimonials WHERE rating = ? AND is_active = 1 ORDER BY created_at DESC',
                [rating]
            );
            return testimonials;
        } catch (error) {
            throw new Error('Error fetching testimonials by rating: ' + error.message);
        }
    }

    // Get random testimonials
    static async getRandom(limit = 3) {
        try {
            const testimonials = await db.query(
                'SELECT * FROM testimonials WHERE is_active = 1 ORDER BY RANDOM() LIMIT ?',
                [limit]
            );
            return testimonials;
        } catch (error) {
            throw new Error('Error fetching random testimonials: ' + error.message);
        }
    }
}

module.exports = Testimonial;
