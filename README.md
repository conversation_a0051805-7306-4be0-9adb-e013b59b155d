# Infinite Loop - Professional Website with Database

A modern, responsive website built with Bootstrap 4.0 and powered by a robust Node.js backend with SQLite database integration.

## 🚀 Features

### Frontend
- **Responsive Design**: Bootstrap 4.0 with mobile-first approach
- **Parallax Effects**: Smooth scrolling and parallax backgrounds
- **Interactive Gallery**: Image carousel with lightbox functionality
- **Dynamic Testimonials**: Rotating customer testimonials
- **Contact Form**: AJAX-powered contact form with validation
- **Modern UI**: Font Awesome icons and smooth animations

### Backend
- **RESTful API**: Complete REST API for all data operations
- **SQLite Database**: Lightweight, serverless database
- **Security**: Helmet.js, CORS, rate limiting, input sanitization
- **Validation**: Express-validator for data validation
- **Error Handling**: Comprehensive error handling and logging
- **Documentation**: Auto-generated API documentation

### Database Features
- **Testimonials Management**: CRUD operations for customer testimonials
- **Gallery Management**: Image gallery with categories and sorting
- **Contact Messages**: Store and manage contact form submissions
- **Services Management**: Dynamic services section
- **Settings**: Configurable website settings
- **Analytics**: Basic visitor tracking

## 📋 Prerequisites

- Node.js (v14 or higher)
- npm or yarn package manager

## 🛠️ Installation

1. **Clone or navigate to the project directory**
   ```bash
   cd 2117_infinite_loop
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Initialize the database**
   ```bash
   npm run init-db
   ```

4. **Start the server**
   ```bash
   npm start
   ```

   For development with auto-restart:
   ```bash
   npm run dev
   ```

5. **Open your browser**
   ```
   http://localhost:3000
   ```

## 📁 Project Structure

```
2117_infinite_loop/
├── 2117_infinite_loop/          # Frontend assets
│   ├── css/                     # Stylesheets
│   ├── js/                      # JavaScript files
│   ├── img/                     # Images
│   ├── fontawesome-5.5/         # Font Awesome icons
│   └── index.html               # Main HTML file
├── config/
│   └── database.js              # Database configuration
├── models/                      # Data models
│   ├── Testimonial.js
│   ├── Gallery.js
│   ├── Contact.js
│   └── Service.js
├── routes/                      # API routes
│   ├── testimonials.js
│   ├── gallery.js
│   ├── contact.js
│   └── services.js
├── middleware/
│   └── security.js              # Security middleware
├── scripts/
│   └── init-database.js         # Database initialization
├── database/                    # SQLite database files
├── server.js                    # Main server file
├── package.json                 # Dependencies and scripts
└── .env                         # Environment variables
```

## 🔧 API Endpoints

### Testimonials
- `GET /api/testimonials` - Get all testimonials
- `GET /api/testimonials/random?limit=3` - Get random testimonials
- `POST /api/testimonials` - Create new testimonial
- `PUT /api/testimonials/:id` - Update testimonial
- `DELETE /api/testimonials/:id` - Delete testimonial

### Gallery
- `GET /api/gallery` - Get all gallery items
- `GET /api/gallery/featured?limit=6` - Get featured items
- `GET /api/gallery/categories` - Get all categories
- `POST /api/gallery` - Create new gallery item
- `PUT /api/gallery/:id` - Update gallery item
- `DELETE /api/gallery/:id` - Delete gallery item

### Contact
- `GET /api/contact` - Get all contact messages
- `POST /api/contact` - Send new contact message
- `GET /api/contact/statistics` - Get contact statistics
- `PUT /api/contact/:id/status` - Update message status

### Services
- `GET /api/services` - Get all services
- `POST /api/services` - Create new service
- `PUT /api/services/:id` - Update service
- `PUT /api/services/reorder` - Reorder services

## 🔒 Security Features

- **Helmet.js**: Security headers
- **CORS**: Cross-origin resource sharing protection
- **Rate Limiting**: Prevents abuse and DDoS attacks
- **Input Sanitization**: XSS protection
- **Validation**: Server-side data validation
- **Error Handling**: Secure error responses

## 🎨 Customization

### Adding New Testimonials
```javascript
const testimonialData = {
    name: "John Doe",
    position: "CEO",
    company: "Tech Corp",
    message: "Great service!",
    rating: 5,
    image_url: "path/to/image.jpg"
};

await api.createTestimonial(testimonialData);
```

### Adding Gallery Items
```javascript
const galleryData = {
    title: "Beautiful Sunset",
    description: "Amazing sunset photography",
    image_url: "path/to/full-image.jpg",
    thumbnail_url: "path/to/thumbnail.jpg",
    category: "nature"
};

await api.createGalleryItem(galleryData);
```

## 🌐 Environment Variables

Create a `.env` file with the following variables:

```env
PORT=3000
NODE_ENV=development
DB_PATH=./database/infinite_loop.db
SESSION_SECRET=your-secret-key
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## 📊 Database Schema

### Testimonials Table
- `id` - Primary key
- `name` - Customer name
- `position` - Job position
- `company` - Company name
- `message` - Testimonial message
- `image_url` - Profile image URL
- `rating` - Rating (1-5)
- `is_active` - Active status
- `created_at` - Creation timestamp

### Gallery Table
- `id` - Primary key
- `title` - Image title
- `description` - Image description
- `image_url` - Full image URL
- `thumbnail_url` - Thumbnail URL
- `category` - Image category
- `sort_order` - Display order
- `is_active` - Active status

### Contact Messages Table
- `id` - Primary key
- `name` - Sender name
- `email` - Sender email
- `subject` - Message subject
- `message` - Message content
- `status` - Message status (new, read, replied, archived)
- `ip_address` - Sender IP
- `user_agent` - Browser information

## 🚀 Deployment

### Production Setup
1. Set `NODE_ENV=production` in your environment
2. Update allowed origins in CORS settings
3. Use a process manager like PM2
4. Set up reverse proxy with Nginx
5. Enable HTTPS

### PM2 Deployment
```bash
npm install -g pm2
pm2 start server.js --name "infinite-loop"
pm2 startup
pm2 save
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🙏 Credits

- **Template**: Tooplate 2117 Infinite Loop
- **Framework**: Bootstrap 4.0
- **Icons**: Font Awesome 5.5
- **Database**: SQLite3
- **Backend**: Node.js + Express

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>
- Documentation: http://localhost:3000/api

---

Made with ❤️ by the Infinite Loop Team
