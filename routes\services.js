const express = require('express');
const { body, validationResult } = require('express-validator');
const Service = require('../models/Service');
const router = express.Router();

// Validation middleware
const validateService = [
    body('title').trim().isLength({ min: 2, max: 100 }).withMessage('Title must be between 2 and 100 characters'),
    body('description').trim().isLength({ min: 10, max: 1000 }).withMessage('Description must be between 10 and 1000 characters'),
    body('icon').trim().isLength({ min: 3, max: 50 }).withMessage('Icon class must be between 3 and 50 characters'),
    body('sort_order').optional().isInt({ min: 0 }).withMessage('Sort order must be a positive integer')
];

// GET /api/services - Get all services
router.get('/', async (req, res) => {
    try {
        const { page, limit } = req.query;
        
        if (page && limit) {
            const result = await Service.getPaginated(
                parseInt(page) || 1, 
                parseInt(limit) || 10
            );
            res.json({
                success: true,
                data: result
            });
        } else {
            const services = await Service.getAll();
            res.json({
                success: true,
                data: services
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// GET /api/services/:id - Get service by ID
router.get('/:id', async (req, res) => {
    try {
        const service = await Service.getById(req.params.id);
        if (!service) {
            return res.status(404).json({
                success: false,
                message: 'Service not found'
            });
        }
        res.json({
            success: true,
            data: service
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// POST /api/services - Create new service
router.post('/', validateService, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation errors',
                errors: errors.array()
            });
        }

        const serviceId = await Service.create(req.body);
        const service = await Service.getById(serviceId);
        
        res.status(201).json({
            success: true,
            message: 'Service created successfully',
            data: service
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// PUT /api/services/:id - Update service
router.put('/:id', validateService, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation errors',
                errors: errors.array()
            });
        }

        const updated = await Service.update(req.params.id, req.body);
        if (!updated) {
            return res.status(404).json({
                success: false,
                message: 'Service not found'
            });
        }

        const service = await Service.getById(req.params.id);
        res.json({
            success: true,
            message: 'Service updated successfully',
            data: service
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// PUT /api/services/:id/toggle-status - Toggle service status
router.put('/:id/toggle-status', async (req, res) => {
    try {
        const updated = await Service.toggleStatus(req.params.id);
        if (!updated) {
            return res.status(404).json({
                success: false,
                message: 'Service not found'
            });
        }

        const service = await Service.getById(req.params.id);
        res.json({
            success: true,
            message: 'Service status updated successfully',
            data: service
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// PUT /api/services/reorder - Reorder services
router.put('/reorder', async (req, res) => {
    try {
        const { services } = req.body;
        
        if (!Array.isArray(services)) {
            return res.status(400).json({
                success: false,
                message: 'Services must be an array'
            });
        }

        // Validate each service object
        for (const service of services) {
            if (!service.id || typeof service.sort_order !== 'number') {
                return res.status(400).json({
                    success: false,
                    message: 'Each service must have id and sort_order'
                });
            }
        }

        await Service.reorder(services);
        res.json({
            success: true,
            message: 'Services reordered successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// DELETE /api/services/:id - Delete service
router.delete('/:id', async (req, res) => {
    try {
        const deleted = await Service.delete(req.params.id);
        if (!deleted) {
            return res.status(404).json({
                success: false,
                message: 'Service not found'
            });
        }

        res.json({
            success: true,
            message: 'Service deleted successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

module.exports = router;
