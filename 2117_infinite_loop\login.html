<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - ELAshrafy</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="fontawesome-5.5/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            min-height: 600px;
        }
        
        .login-left {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .login-left::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .brand-logo {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }
        
        .brand-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            position: relative;
            z-index: 2;
        }
        
        .feature-list li {
            margin: 1rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .feature-list i {
            margin-left: 10px;
            font-size: 1.2rem;
        }
        
        .login-right {
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .login-form {
            max-width: 400px;
            margin: 0 auto;
            width: 100%;
        }
        
        .form-title {
            text-align: center;
            margin-bottom: 2rem;
            color: #333;
        }
        
        .form-title h2 {
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .form-title p {
            color: #666;
            margin: 0;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .form-control {
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
        }
        
        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
            font-size: 1.1rem;
        }
        
        .form-control.with-icon {
            padding-left: 50px;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-login:active {
            transform: translateY(0);
        }
        
        .btn-login.loading {
            pointer-events: none;
        }
        
        .btn-login .spinner {
            display: none;
        }
        
        .btn-login.loading .spinner {
            display: inline-block;
        }
        
        .btn-login.loading .btn-text {
            display: none;
        }
        
        .form-check {
            margin: 1.5rem 0;
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .form-check-label {
            color: #666;
            cursor: pointer;
        }
        
        .divider {
            text-align: center;
            margin: 2rem 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e1e5e9;
        }
        
        .divider span {
            background: white;
            padding: 0 1rem;
            color: #666;
        }
        
        .register-link {
            text-align: center;
            margin-top: 2rem;
        }
        
        .register-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        
        .register-link a:hover {
            text-decoration: underline;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
            color: white;
        }
        
        .back-to-home {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            text-decoration: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .back-to-home:hover {
            color: white;
            text-decoration: none;
            transform: translateX(5px);
        }
        
        @media (max-width: 768px) {
            .login-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .login-left {
                padding: 40px 20px;
                min-height: 300px;
            }
            
            .login-right {
                padding: 40px 20px;
            }
            
            .brand-logo {
                font-size: 2rem;
            }
            
            .brand-subtitle {
                font-size: 1rem;
            }
        }
        
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float-shapes 15s infinite ease-in-out;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 20%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 20%;
            animation-delay: 5s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 30%;
            animation-delay: 10s;
        }
        
        @keyframes float-shapes {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-30px) rotate(180deg);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <a href="/" class="back-to-home">
        <i class="fas fa-arrow-right"></i> العودة للموقع
    </a>
    
    <div class="login-container">
        <div class="row no-gutters h-100">
            <!-- Left Side - Branding -->
            <div class="col-lg-6 login-left">
                <div class="floating-shapes">
                    <div class="shape"></div>
                    <div class="shape"></div>
                    <div class="shape"></div>
                </div>
                
                <div class="brand-logo">
                    <i class="fas fa-crown"></i> ELAshrafy
                </div>
                <p class="brand-subtitle">منصة إدارة المحتوى الاحترافية</p>
                
                <ul class="feature-list">
                    <li>
                        <i class="fas fa-shield-alt"></i>
                        حماية متقدمة وأمان عالي
                    </li>
                    <li>
                        <i class="fas fa-chart-line"></i>
                        إحصائيات وتقارير مفصلة
                    </li>
                    <li>
                        <i class="fas fa-users"></i>
                        إدارة المستخدمين والصلاحيات
                    </li>
                    <li>
                        <i class="fas fa-mobile-alt"></i>
                        متوافق مع جميع الأجهزة
                    </li>
                </ul>
            </div>
            
            <!-- Right Side - Login Form -->
            <div class="col-lg-6 login-right">
                <div class="login-form">
                    <div class="form-title">
                        <h2>مرحباً بك</h2>
                        <p>قم بتسجيل الدخول للوصول إلى لوحة التحكم</p>
                    </div>
                    
                    <div id="alert-container"></div>
                    
                    <form id="loginForm">
                        <div class="form-group">
                            <i class="fas fa-envelope input-icon"></i>
                            <input type="email" class="form-control with-icon" id="email" name="email" 
                                   placeholder="البريد الإلكتروني" required>
                        </div>
                        
                        <div class="form-group">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" class="form-control with-icon" id="password" name="password" 
                                   placeholder="كلمة المرور" required>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="remember_me" name="remember_me">
                            <label class="form-check-label" for="remember_me">
                                تذكرني
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-login" id="loginBtn">
                            <span class="btn-text">
                                <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                            </span>
                            <span class="spinner">
                                <i class="fas fa-spinner fa-spin"></i> جاري التحقق...
                            </span>
                        </button>
                    </form>
                    
                    <div class="divider">
                        <span>أو</span>
                    </div>
                    
                    <div class="register-link">
                        <p>ليس لديك حساب؟ <a href="#" onclick="showRegisterForm()">إنشاء حساب جديد</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/jquery-1.9.1.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/api.js"></script>
    
    <script>
        class LoginManager {
            constructor() {
                this.form = document.getElementById('loginForm');
                this.loginBtn = document.getElementById('loginBtn');
                this.alertContainer = document.getElementById('alert-container');
                this.init();
            }

            init() {
                this.form.addEventListener('submit', this.handleLogin.bind(this));
                this.checkIfLoggedIn();
            }

            async checkIfLoggedIn() {
                try {
                    const response = await api.get('/auth/me');
                    if (response.success) {
                        // User is already logged in, redirect to admin
                        window.location.href = '/admin.html';
                    }
                } catch (error) {
                    // User is not logged in, continue with login form
                }
            }

            async handleLogin(event) {
                event.preventDefault();
                
                const formData = new FormData(this.form);
                const loginData = {
                    email: formData.get('email'),
                    password: formData.get('password'),
                    remember_me: formData.get('remember_me') === 'on'
                };

                this.setLoading(true);
                this.clearAlerts();

                try {
                    const response = await api.post('/auth/login', loginData);
                    
                    if (response.success) {
                        this.showAlert('تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');
                        
                        // Store user data
                        localStorage.setItem('user', JSON.stringify(response.data.user));
                        localStorage.setItem('token', response.data.token);
                        
                        // Redirect after short delay
                        setTimeout(() => {
                            window.location.href = '/admin.html';
                        }, 1500);
                    } else {
                        this.showAlert(response.message || 'فشل في تسجيل الدخول', 'danger');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    this.showAlert('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.', 'danger');
                } finally {
                    this.setLoading(false);
                }
            }

            setLoading(loading) {
                if (loading) {
                    this.loginBtn.classList.add('loading');
                    this.loginBtn.disabled = true;
                } else {
                    this.loginBtn.classList.remove('loading');
                    this.loginBtn.disabled = false;
                }
            }

            showAlert(message, type) {
                const alertHTML = `
                    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="close" data-dismiss="alert">
                            <span>&times;</span>
                        </button>
                    </div>
                `;
                this.alertContainer.innerHTML = alertHTML;
            }

            clearAlerts() {
                this.alertContainer.innerHTML = '';
            }
        }

        function showRegisterForm() {
            alert('صفحة التسجيل قيد التطوير. يرجى التواصل مع المدير لإنشاء حساب جديد.');
        }

        // Initialize login manager when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new LoginManager();
        });

        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', () => {
            // Add focus effects to form inputs
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });
                
                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });
            });

            // Add ripple effect to login button
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');
                
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    </script>
    
    <style>
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        .form-group.focused .input-icon {
            color: #667eea;
            transform: translateY(-50%) scale(1.1);
        }
    </style>
</body>
</html>
