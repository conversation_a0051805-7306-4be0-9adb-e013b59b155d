// Test script for Infinite Loop API
const fetch = require('node-fetch');

const API_BASE = 'http://localhost:3000/api';

class APITester {
    async request(endpoint, options = {}) {
        const url = `${API_BASE}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();
            return { status: response.status, data };
        } catch (error) {
            return { error: error.message };
        }
    }

    async testHealthCheck() {
        console.log('🔍 Testing Health Check...');
        const result = await this.request('/health');
        console.log('Status:', result.status);
        console.log('Response:', result.data);
        console.log('---');
    }

    async testTestimonials() {
        console.log('💬 Testing Testimonials API...');
        
        // Get all testimonials
        console.log('Getting all testimonials...');
        const allTestimonials = await this.request('/testimonials');
        console.log('Status:', allTestimonials.status);
        console.log('Count:', allTestimonials.data?.data?.length || 0);
        
        // Get random testimonials
        console.log('Getting random testimonials...');
        const randomTestimonials = await this.request('/testimonials/random?limit=2');
        console.log('Status:', randomTestimonials.status);
        console.log('Count:', randomTestimonials.data?.data?.length || 0);
        
        // Create new testimonial
        console.log('Creating new testimonial...');
        const newTestimonial = {
            name: 'Test User',
            position: 'Software Developer',
            company: 'Test Company',
            message: 'This is a test testimonial created via API.',
            rating: 5
        };
        
        const createResult = await this.request('/testimonials', {
            method: 'POST',
            body: JSON.stringify(newTestimonial)
        });
        console.log('Create Status:', createResult.status);
        console.log('Created ID:', createResult.data?.data?.id);
        console.log('---');
    }

    async testGallery() {
        console.log('🖼️ Testing Gallery API...');
        
        // Get all gallery items
        console.log('Getting all gallery items...');
        const allGallery = await this.request('/gallery');
        console.log('Status:', allGallery.status);
        console.log('Count:', allGallery.data?.data?.length || 0);
        
        // Get featured gallery items
        console.log('Getting featured gallery items...');
        const featuredGallery = await this.request('/gallery/featured?limit=3');
        console.log('Status:', featuredGallery.status);
        console.log('Count:', featuredGallery.data?.data?.length || 0);
        
        // Get categories
        console.log('Getting gallery categories...');
        const categories = await this.request('/gallery/categories');
        console.log('Status:', categories.status);
        console.log('Categories:', categories.data?.data);
        console.log('---');
    }

    async testServices() {
        console.log('⚙️ Testing Services API...');
        
        // Get all services
        console.log('Getting all services...');
        const allServices = await this.request('/services');
        console.log('Status:', allServices.status);
        console.log('Count:', allServices.data?.data?.length || 0);
        
        if (allServices.data?.data?.length > 0) {
            const firstService = allServices.data.data[0];
            console.log('First service:', firstService.title);
        }
        console.log('---');
    }

    async testContact() {
        console.log('📧 Testing Contact API...');
        
        // Send test contact message
        console.log('Sending test contact message...');
        const contactMessage = {
            name: 'Test Contact',
            email: '<EMAIL>',
            subject: 'Test Message',
            message: 'This is a test message sent via API.'
        };
        
        const sendResult = await this.request('/contact', {
            method: 'POST',
            body: JSON.stringify(contactMessage)
        });
        console.log('Send Status:', sendResult.status);
        console.log('Message:', sendResult.data?.message);
        
        // Get contact statistics
        console.log('Getting contact statistics...');
        const stats = await this.request('/contact/statistics');
        console.log('Stats Status:', stats.status);
        console.log('Total Messages:', stats.data?.data?.total || 0);
        console.log('---');
    }

    async runAllTests() {
        console.log('🚀 Starting API Tests for Infinite Loop Website\n');
        
        try {
            await this.testHealthCheck();
            await this.testTestimonials();
            await this.testGallery();
            await this.testServices();
            await this.testContact();
            
            console.log('✅ All tests completed successfully!');
        } catch (error) {
            console.error('❌ Test failed:', error);
        }
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    const tester = new APITester();
    tester.runAllTests();
}

module.exports = APITester;
