require('dotenv').config();
const db = require('../config/database');
const Gallery = require('../models/Gallery');
const User = require('../models/User');

async function initializeDatabase() {
    console.log('🚀 Initializing ELAshrafy Database...\n');

    try {
        // Wait for database to be ready
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Add sample gallery items
        console.log('📸 Adding sample gallery items...');
        const galleryItems = [
            {
                title: 'Physical Health',
                description: 'Exercise and fitness activities',
                image_url: 'img/gallery-img-01.jpg',
                thumbnail_url: 'img/gallery-tn-01.jpg',
                category: 'health',
                sort_order: 1
            },
            {
                title: 'Rain on Glass',
                description: 'Beautiful nature photography',
                image_url: 'img/gallery-img-02.jpg',
                thumbnail_url: 'img/gallery-tn-02.jpg',
                category: 'nature',
                sort_order: 2
            },
            {
                title: 'Sea View',
                description: 'Mega city from the sea',
                image_url: 'img/gallery-img-03.jpg',
                thumbnail_url: 'img/gallery-tn-03.jpg',
                category: 'city',
                sort_order: 3
            },
            {
                title: 'Dream Girl',
                description: 'Portrait photography',
                image_url: 'img/gallery-img-04.jpg',
                thumbnail_url: 'img/gallery-tn-04.jpg',
                category: 'portrait',
                sort_order: 4
            },
            {
                title: 'Workstation',
                description: 'Modern office spaces',
                image_url: 'img/gallery-img-05.jpg',
                thumbnail_url: 'img/gallery-tn-05.jpg',
                category: 'business',
                sort_order: 5
            },
            {
                title: 'Just Above',
                description: 'The city from above',
                image_url: 'img/gallery-img-06.jpg',
                thumbnail_url: 'img/gallery-tn-06.jpg',
                category: 'city',
                sort_order: 6
            }
        ];

        for (const item of galleryItems) {
            try {
                await Gallery.create(item);
                console.log(`✅ Added gallery item: ${item.title}`);
            } catch (error) {
                if (error.message.includes('UNIQUE constraint failed')) {
                    console.log(`⚠️  Gallery item already exists: ${item.title}`);
                } else {
                    console.error(`❌ Error adding gallery item ${item.title}:`, error.message);
                }
            }
        }

        // Add more sample testimonials
        console.log('\n💬 Adding additional testimonials...');
        const additionalTestimonials = [
            {
                name: 'Sandar Soft',
                position: 'Marketing Manager',
                company: 'Digital Agency',
                message: 'Nulla finibus ligula nec tortor convallis tincidunt. Interdum et malesuada fames ac ante ipsum primis in faucibus.',
                image_url: 'img/testimonial-img-03.jpg',
                rating: 5
            },
            {
                name: 'Oliva Htoo',
                position: 'UI/UX Designer',
                company: 'Creative Studio',
                message: 'Curabitur rutrum pharetra lobortis. Pellentesque vehicula, velit quis eleifend fermentum, erat arcu aliquet neque.',
                image_url: 'img/testimonial-img-04.jpg',
                rating: 5
            },
            {
                name: 'Jacob Joker',
                position: 'CTO',
                company: 'Tech Startup',
                message: 'Integer sit amet risus et erat imperdiet finibus. Nam lacus nunc, vulputate id ex eget, euismod auctor augue.',
                image_url: 'img/testimonial-img-02.jpg',
                rating: 4
            }
        ];

        const Testimonial = require('../models/Testimonial');
        for (const testimonial of additionalTestimonials) {
            try {
                await Testimonial.create(testimonial);
                console.log(`✅ Added testimonial: ${testimonial.name}`);
            } catch (error) {
                console.log(`⚠️  Testimonial might already exist: ${testimonial.name}`);
            }
        }

        // Create default admin user
        console.log('\n👤 Creating default admin user...');
        await User.createDefaultAdmin();

        // Add website settings
        console.log('\n⚙️  Updating website settings...');
        const settings = [
            { key: 'hero_title', value: 'ELAshrafy', description: 'Main hero title' },
            { key: 'hero_subtitle', value: 'Professional Website Platform', description: 'Hero subtitle' },
            { key: 'hero_description', value: 'Powered by Advanced Technology', description: 'Hero description' },
            { key: 'about_title', value: 'What We Do', description: 'About section title' },
            { key: 'gallery_title', value: 'Gallery', description: 'Gallery section title' },
            { key: 'testimonials_title', value: 'Testimonials', description: 'Testimonials section title' },
            { key: 'contact_title', value: 'Contact Us', description: 'Contact section title' }
        ];

        for (const setting of settings) {
            try {
                await db.run(
                    'INSERT OR REPLACE INTO settings (setting_key, setting_value, description) VALUES (?, ?, ?)',
                    [setting.key, setting.value, setting.description]
                );
                console.log(`✅ Updated setting: ${setting.key}`);
            } catch (error) {
                console.error(`❌ Error updating setting ${setting.key}:`, error.message);
            }
        }

        console.log('\n🎉 Database initialization completed successfully!');
        console.log('\n📊 Database Summary:');
        
        // Show statistics
        const testimonialCount = await db.get('SELECT COUNT(*) as count FROM testimonials WHERE is_active = 1');
        const galleryCount = await db.get('SELECT COUNT(*) as count FROM gallery WHERE is_active = 1');
        const serviceCount = await db.get('SELECT COUNT(*) as count FROM services WHERE is_active = 1');
        const settingCount = await db.get('SELECT COUNT(*) as count FROM settings');

        console.log(`   • Testimonials: ${testimonialCount.count}`);
        console.log(`   • Gallery Items: ${galleryCount.count}`);
        console.log(`   • Services: ${serviceCount.count}`);
        console.log(`   • Settings: ${settingCount.count}`);

        console.log('\n🌐 You can now start the server with: npm start');
        console.log('📱 Visit: http://localhost:3000');

    } catch (error) {
        console.error('❌ Database initialization failed:', error);
        process.exit(1);
    } finally {
        // Close database connection
        await db.close();
        process.exit(0);
    }
}

// Run initialization
initializeDatabase();
