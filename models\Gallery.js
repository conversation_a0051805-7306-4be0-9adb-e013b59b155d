const db = require('../config/database');

class Gallery {
    // Get all active gallery items
    static async getAll() {
        try {
            const items = await db.query(
                'SELECT * FROM gallery WHERE is_active = 1 ORDER BY sort_order ASC, created_at DESC'
            );
            return items;
        } catch (error) {
            throw new Error('Error fetching gallery items: ' + error.message);
        }
    }

    // Get gallery item by ID
    static async getById(id) {
        try {
            const item = await db.get(
                'SELECT * FROM gallery WHERE id = ?',
                [id]
            );
            return item;
        } catch (error) {
            throw new Error('Error fetching gallery item: ' + error.message);
        }
    }

    // Create new gallery item
    static async create(data) {
        try {
            const { title, description, image_url, thumbnail_url, category, sort_order } = data;
            const result = await db.run(
                `INSERT INTO gallery (title, description, image_url, thumbnail_url, category, sort_order) 
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [title, description || null, image_url, thumbnail_url || null, category || 'general', sort_order || 0]
            );
            return result.id;
        } catch (error) {
            throw new Error('Error creating gallery item: ' + error.message);
        }
    }

    // Update gallery item
    static async update(id, data) {
        try {
            const { title, description, image_url, thumbnail_url, category, sort_order, is_active } = data;
            const result = await db.run(
                `UPDATE gallery 
                 SET title = ?, description = ?, image_url = ?, thumbnail_url = ?, 
                     category = ?, sort_order = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                 WHERE id = ?`,
                [title, description, image_url, thumbnail_url, category, sort_order, is_active, id]
            );
            return result.changes > 0;
        } catch (error) {
            throw new Error('Error updating gallery item: ' + error.message);
        }
    }

    // Delete gallery item (soft delete)
    static async delete(id) {
        try {
            const result = await db.run(
                'UPDATE gallery SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [id]
            );
            return result.changes > 0;
        } catch (error) {
            throw new Error('Error deleting gallery item: ' + error.message);
        }
    }

    // Get gallery items by category
    static async getByCategory(category) {
        try {
            const items = await db.query(
                'SELECT * FROM gallery WHERE category = ? AND is_active = 1 ORDER BY sort_order ASC, created_at DESC',
                [category]
            );
            return items;
        } catch (error) {
            throw new Error('Error fetching gallery items by category: ' + error.message);
        }
    }

    // Get all categories
    static async getCategories() {
        try {
            const categories = await db.query(
                'SELECT DISTINCT category FROM gallery WHERE is_active = 1 ORDER BY category'
            );
            return categories.map(row => row.category);
        } catch (error) {
            throw new Error('Error fetching gallery categories: ' + error.message);
        }
    }

    // Get gallery items with pagination
    static async getPaginated(page = 1, limit = 12, category = null) {
        try {
            const offset = (page - 1) * limit;
            let query = 'SELECT * FROM gallery WHERE is_active = 1';
            let countQuery = 'SELECT COUNT(*) as total FROM gallery WHERE is_active = 1';
            let params = [];
            let countParams = [];

            if (category) {
                query += ' AND category = ?';
                countQuery += ' AND category = ?';
                params.push(category);
                countParams.push(category);
            }

            query += ' ORDER BY sort_order ASC, created_at DESC LIMIT ? OFFSET ?';
            params.push(limit, offset);

            const items = await db.query(query, params);
            const totalResult = await db.get(countQuery, countParams);

            return {
                items,
                total: totalResult.total,
                page,
                limit,
                totalPages: Math.ceil(totalResult.total / limit),
                category
            };
        } catch (error) {
            throw new Error('Error fetching paginated gallery items: ' + error.message);
        }
    }

    // Update sort order
    static async updateSortOrder(id, sortOrder) {
        try {
            const result = await db.run(
                'UPDATE gallery SET sort_order = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [sortOrder, id]
            );
            return result.changes > 0;
        } catch (error) {
            throw new Error('Error updating sort order: ' + error.message);
        }
    }

    // Get featured gallery items
    static async getFeatured(limit = 6) {
        try {
            const items = await db.query(
                'SELECT * FROM gallery WHERE is_active = 1 ORDER BY sort_order ASC LIMIT ?',
                [limit]
            );
            return items;
        } catch (error) {
            throw new Error('Error fetching featured gallery items: ' + error.message);
        }
    }
}

module.exports = Gallery;
