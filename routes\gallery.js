const express = require('express');
const { body, validationResult } = require('express-validator');
const Gallery = require('../models/Gallery');
const router = express.Router();

// Validation middleware
const validateGalleryItem = [
    body('title').trim().isLength({ min: 2, max: 100 }).withMessage('Title must be between 2 and 100 characters'),
    body('description').optional().trim().isLength({ max: 500 }).withMessage('Description must not exceed 500 characters'),
    body('image_url').isURL().withMessage('Image URL must be valid'),
    body('thumbnail_url').optional().isURL().withMessage('Thumbnail URL must be valid'),
    body('category').optional().trim().isLength({ max: 50 }).withMessage('Category must not exceed 50 characters'),
    body('sort_order').optional().isInt({ min: 0 }).withMessage('Sort order must be a positive integer')
];

// GET /api/gallery - Get all gallery items
router.get('/', async (req, res) => {
    try {
        const { page, limit, category } = req.query;
        
        if (page && limit) {
            const result = await Gallery.getPaginated(
                parseInt(page) || 1, 
                parseInt(limit) || 12,
                category
            );
            res.json({
                success: true,
                data: result
            });
        } else if (category) {
            const items = await Gallery.getByCategory(category);
            res.json({
                success: true,
                data: items
            });
        } else {
            const items = await Gallery.getAll();
            res.json({
                success: true,
                data: items
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// GET /api/gallery/categories - Get all categories
router.get('/categories', async (req, res) => {
    try {
        const categories = await Gallery.getCategories();
        res.json({
            success: true,
            data: categories
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// GET /api/gallery/featured - Get featured gallery items
router.get('/featured', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 6;
        const items = await Gallery.getFeatured(limit);
        res.json({
            success: true,
            data: items
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// GET /api/gallery/:id - Get gallery item by ID
router.get('/:id', async (req, res) => {
    try {
        const item = await Gallery.getById(req.params.id);
        if (!item) {
            return res.status(404).json({
                success: false,
                message: 'Gallery item not found'
            });
        }
        res.json({
            success: true,
            data: item
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// POST /api/gallery - Create new gallery item
router.post('/', validateGalleryItem, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation errors',
                errors: errors.array()
            });
        }

        const itemId = await Gallery.create(req.body);
        const item = await Gallery.getById(itemId);
        
        res.status(201).json({
            success: true,
            message: 'Gallery item created successfully',
            data: item
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// PUT /api/gallery/:id - Update gallery item
router.put('/:id', validateGalleryItem, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation errors',
                errors: errors.array()
            });
        }

        const updated = await Gallery.update(req.params.id, req.body);
        if (!updated) {
            return res.status(404).json({
                success: false,
                message: 'Gallery item not found'
            });
        }

        const item = await Gallery.getById(req.params.id);
        res.json({
            success: true,
            message: 'Gallery item updated successfully',
            data: item
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// PUT /api/gallery/:id/sort-order - Update sort order
router.put('/:id/sort-order', async (req, res) => {
    try {
        const { sort_order } = req.body;
        if (typeof sort_order !== 'number' || sort_order < 0) {
            return res.status(400).json({
                success: false,
                message: 'Sort order must be a positive number'
            });
        }

        const updated = await Gallery.updateSortOrder(req.params.id, sort_order);
        if (!updated) {
            return res.status(404).json({
                success: false,
                message: 'Gallery item not found'
            });
        }

        res.json({
            success: true,
            message: 'Sort order updated successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// DELETE /api/gallery/:id - Delete gallery item
router.delete('/:id', async (req, res) => {
    try {
        const deleted = await Gallery.delete(req.params.id);
        if (!deleted) {
            return res.status(404).json({
                success: false,
                message: 'Gallery item not found'
            });
        }

        res.json({
            success: true,
            message: 'Gallery item deleted successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

module.exports = router;
