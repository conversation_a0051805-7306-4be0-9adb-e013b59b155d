const db = require('../config/database');
const bcrypt = require('bcryptjs');

class User {
    // Get user by email
    static async getByEmail(email) {
        try {
            const user = await db.get(
                'SELECT * FROM users WHERE email = ?',
                [email]
            );
            return user;
        } catch (error) {
            throw new Error('Error fetching user: ' + error.message);
        }
    }

    // Get user by ID
    static async getById(id) {
        try {
            const user = await db.get(
                'SELECT id, email, username, full_name, role, is_active, created_at FROM users WHERE id = ?',
                [id]
            );
            return user;
        } catch (error) {
            throw new Error('Error fetching user: ' + error.message);
        }
    }

    // Create new user
    static async create(userData) {
        try {
            const { email, password, username, full_name, role = 'user' } = userData;
            
            // Check if user already exists
            const existingUser = await this.getByEmail(email);
            if (existingUser) {
                throw new Error('User with this email already exists');
            }

            // Hash password
            const saltRounds = 12;
            const hashedPassword = await bcrypt.hash(password, saltRounds);

            const result = await db.run(
                `INSERT INTO users (email, password, username, full_name, role) 
                 VALUES (?, ?, ?, ?, ?)`,
                [email, hashedPassword, username, full_name, role]
            );
            
            return result.id;
        } catch (error) {
            throw new Error('Error creating user: ' + error.message);
        }
    }

    // Verify password
    static async verifyPassword(plainPassword, hashedPassword) {
        try {
            return await bcrypt.compare(plainPassword, hashedPassword);
        } catch (error) {
            throw new Error('Error verifying password: ' + error.message);
        }
    }

    // Authenticate user
    static async authenticate(email, password) {
        try {
            const user = await this.getByEmail(email);
            if (!user) {
                return null;
            }

            if (!user.is_active) {
                throw new Error('Account is deactivated');
            }

            const isValidPassword = await this.verifyPassword(password, user.password);
            if (!isValidPassword) {
                return null;
            }

            // Update last login
            await db.run(
                'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
                [user.id]
            );

            // Return user without password
            const { password: _, ...userWithoutPassword } = user;
            return userWithoutPassword;
        } catch (error) {
            throw new Error('Error authenticating user: ' + error.message);
        }
    }

    // Update user
    static async update(id, userData) {
        try {
            const { email, username, full_name, role, is_active } = userData;
            const result = await db.run(
                `UPDATE users 
                 SET email = ?, username = ?, full_name = ?, role = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                 WHERE id = ?`,
                [email, username, full_name, role, is_active, id]
            );
            return result.changes > 0;
        } catch (error) {
            throw new Error('Error updating user: ' + error.message);
        }
    }

    // Change password
    static async changePassword(id, newPassword) {
        try {
            const saltRounds = 12;
            const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
            
            const result = await db.run(
                'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [hashedPassword, id]
            );
            return result.changes > 0;
        } catch (error) {
            throw new Error('Error changing password: ' + error.message);
        }
    }

    // Get all users (admin only)
    static async getAll() {
        try {
            const users = await db.query(
                'SELECT id, email, username, full_name, role, is_active, created_at, last_login FROM users ORDER BY created_at DESC'
            );
            return users;
        } catch (error) {
            throw new Error('Error fetching users: ' + error.message);
        }
    }

    // Delete user (soft delete)
    static async delete(id) {
        try {
            const result = await db.run(
                'UPDATE users SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [id]
            );
            return result.changes > 0;
        } catch (error) {
            throw new Error('Error deleting user: ' + error.message);
        }
    }

    // Create default admin user
    static async createDefaultAdmin() {
        try {
            const adminEmail = '<EMAIL>';
            const existingAdmin = await this.getByEmail(adminEmail);
            
            if (!existingAdmin) {
                const adminData = {
                    email: adminEmail,
                    password: 'admin123456', // Should be changed after first login
                    username: 'admin',
                    full_name: 'ELAshrafy Administrator',
                    role: 'admin'
                };
                
                const adminId = await this.create(adminData);
                console.log('✅ Default admin user created with ID:', adminId);
                console.log('📧 Email: <EMAIL>');
                console.log('🔑 Password: admin123456 (Please change after first login)');
                return adminId;
            } else {
                console.log('ℹ️  Admin user already exists');
                return existingAdmin.id;
            }
        } catch (error) {
            console.error('❌ Error creating default admin:', error.message);
            throw error;
        }
    }

    // Get user statistics
    static async getStatistics() {
        try {
            const totalUsers = await db.get('SELECT COUNT(*) as total FROM users');
            const activeUsers = await db.get('SELECT COUNT(*) as count FROM users WHERE is_active = 1');
            const adminUsers = await db.get('SELECT COUNT(*) as count FROM users WHERE role = "admin"');
            const recentUsers = await db.get('SELECT COUNT(*) as count FROM users WHERE DATE(created_at) >= DATE("now", "-7 days")');

            return {
                total: totalUsers.total,
                active: activeUsers.count,
                admins: adminUsers.count,
                recent: recentUsers.count
            };
        } catch (error) {
            throw new Error('Error fetching user statistics: ' + error.message);
        }
    }
}

module.exports = User;
